package com.cet.eem.fusion.ef.sdk.service.Impl;

import com.cet.eem.fusion.common.def.common.ColumnDef;
import com.cet.eem.fusion.common.def.common.SplitCharDef;
import com.cet.eem.fusion.common.def.energy.EnumSystemEventType;
import com.cet.eem.fusion.common.def.i18n.EnumerationLangKeyDef;
import com.cet.eem.fusion.common.def.label.ModelLabelDef;
import com.cet.eem.fusion.common.def.label.NodeLabelDef;
import com.cet.eem.fusion.common.model.BaseVo;
import com.cet.eem.fusion.common.model.Node;
import com.cet.eem.fusion.common.model.Page;
import com.cet.eem.fusion.common.model.SystemEventConfirmRequest;
import com.cet.eem.fusion.common.model.datalog.DataLogData;
import com.cet.eem.fusion.common.model.event.RedisEventKey;
import com.cet.eem.fusion.common.model.objective.physicalquantity.AggregationCycle;
import com.cet.eem.fusion.common.model.time.StartEndTime;
import com.cet.eem.fusion.common.modelutils.conditions.query.QueryWrapper;
import com.cet.eem.fusion.common.modelutils.model.base.ConditionBlock;
import com.cet.eem.fusion.common.modelutils.model.base.QueryCondition;
import com.cet.eem.fusion.common.modelutils.model.tool.ModelServiceUtils;
import com.cet.eem.fusion.common.modelutils.model.tool.ParentQueryConditionBuilder;
import com.cet.eem.fusion.common.modelutils.service.EemModelDataService;
import com.cet.eem.fusion.common.service.RedisService;
import com.cet.eem.fusion.common.utils.JsonTransferUtils;
import com.cet.eem.fusion.common.utils.ParamUtils;
import com.cet.eem.fusion.common.utils.SimpleExcelSheetBuilder;
import com.cet.eem.fusion.common.utils.SortUtils;
import com.cet.eem.fusion.common.utils.page.PageUtils;
import com.cet.eem.fusion.common.utils.time.TimeFormatDef;
import com.cet.eem.fusion.common.utils.time.TimeUtil;
import com.cet.eem.fusion.config.sdk.entity.unnatural.UnnaturalSetVo;
import com.cet.eem.fusion.config.sdk.service.ProjectEnergyTypeService;
import com.cet.eem.fusion.config.sdk.service.UnnaturalTimeService;
import com.cet.eem.fusion.ef.sdk.dto.*;
import com.cet.eem.fusion.ef.sdk.entity.EventParamQueryVO;
import com.cet.eem.fusion.ef.sdk.service.AlarmEventService;
import com.cet.eem.fusion.energy.sdk.dao.alarm.AlarmSchemeDao;
import com.cet.eem.fusion.energy.sdk.model.alarmservice.EEMAlarmLevel;
import com.cet.eem.fusion.energy.sdk.model.alarmservice.EEMAlarmScheme;
import com.cet.eem.fusion.energy.sdk.util.DateUtil;
import com.cet.electric.baseconfig.common.base.BaseEntity;
import com.cet.electric.commons.ApiResult;
import com.cet.electric.modelsdk.event.entity.dto.SystemEventExtendDTO;
import com.cet.electric.modelsdk.event.model.SystemEvent;
import com.cet.electric.modelsdk.event.service.SystemEventService;
import com.cet.electric.modelservice.common.entity.IdTextPair;
import com.cet.futureblue.i18n.LanguageUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.cet.eem.fusion.common.def.i18n.CommonLangKeyDef.Config.*;
import static com.cet.eem.fusion.common.def.i18n.EnergyLangKeyDef.CONSUMPTION.*;

/**
 * <AUTHOR>
 * @date 2025年06月30日 下午5:05
 */

@Service
@Slf4j
public class AlarmEventServiceImpl implements AlarmEventService {

    @Resource
    EemModelDataService modelService;

    @Resource
    RedisService redisService;

    @Resource
    SystemEventService systemEventService;

    @Resource
    ProjectEnergyTypeService projectEnergyTypeService;

    @Resource
    UnnaturalTimeService unnaturalTimeService;

    @Resource
    AlarmSchemeDao alarmSchemeDao;

    //能耗预警
    public static final Integer energyconsumpdearlyalarm = 701;

    public static final String CONFIRM_EVENT_STATUS = "confirmeventstatus";

    public static final Integer ALARM_TYPE = 701;


    /**
     * 预警等级
     */
    private final int alarmReturnLevel = 0;

    /**
     * 系统事件收敛匹配的偏移量
     */
    @Value("${eem.event.system-event-offset:200}")
    private int systemEventOffset;

    @Resource
    ModelServiceUtils modelServiceUtils;

    @Override
    public List<SystemEventWithText> queryHandleEvents(EventParamQueryVO eventParam, boolean pageFormat, Long userId, Long tenantId) {
        //处理预警返回的事件等级
        handleEventParam(eventParam);
        List<EventNode> allNodes = getAllNodes(eventParam);

        List<SystemEventWithText> systemEventWithTexts = queryEvents(eventParam, allNodes, Objects.equals(eventParam.getModelLabel(), NodeLabelDef.MANU_EQUIPMENT));
        if (CollectionUtils.isEmpty(systemEventWithTexts)) {
            return Collections.emptyList();
        }
        Map<BaseVo, Node> map = new HashMap<>();
        for (EventNode node : allNodes) {
            Node tmpNode = new Node();
            tmpNode.setLevelName(node.getLevelName());
            tmpNode.setName(node.getName());
            map.put(new BaseVo(node.getId(), node.getModelLabel()), tmpNode);
        }
        // 筛选结束事件赋值持续时间
        setDuringTime(systemEventWithTexts);
        addEventInfos(systemEventWithTexts, map, tenantId);
        // 收敛事件
        if (eventParam.isConvergence()) {
            systemEventWithTexts = handleConvergenceGroup(systemEventWithTexts);
            systemEventWithTexts.sort((v1, v2) -> SortUtils.sort(v1.getEventTime(), v2.getEventTime(), false));
        }

        if (pageFormat) {
            return PageUtils.constructPageResultData(systemEventWithTexts, new Page(eventParam.getIndex(), eventParam.getLimit())).getData();
        }
        return systemEventWithTexts;
    }

    private List<SystemEventWithText> handleConvergenceGroup(List<SystemEventWithText> alarmEvents) {
        //筛选出有收敛标记的事件数据
        List<SystemEventWithText> eventWithGroup = alarmEvents.stream().filter(it -> StringUtils.isNotBlank(it.getConvergentGroup())).collect(Collectors.toList());
        Map<String, List<SystemEventWithText>> collect = eventWithGroup.stream().collect(Collectors.groupingBy(SystemEventVo::getConvergentGroup));
        List<SystemEventWithText> convergenceGroup = new ArrayList<>();
        for (Map.Entry<String, List<SystemEventWithText>> entry : collect.entrySet()) {
            List<SystemEventWithText> value = entry.getValue();
            List<SystemEventWithText> collect1 = value.stream().filter(systemEventWithText -> Boolean.TRUE.equals(systemEventWithText.getConvergentStatus())).collect(Collectors.toList());
            List<SystemEventWithText> collect2 = value.stream().filter(systemEventWithText -> !Boolean.TRUE.equals(systemEventWithText.getConvergentStatus())).collect(Collectors.toList());
            SystemEventWithText systemEventWithText = new SystemEventWithText();
            if (CollectionUtils.isEmpty(collect1)) {
                continue;
            }
            BeanUtils.copyProperties(collect1.get(0), systemEventWithText);
            systemEventWithText.setChildren(collect2);
            convergenceGroup.add(systemEventWithText);
        }
        List<SystemEventWithText> eventWithNonGroup = alarmEvents.stream().filter(systemEventWithText -> StringUtils.isBlank(systemEventWithText.getConvergentGroup())).collect(Collectors.toList());
        convergenceGroup.addAll(eventWithNonGroup);
        return convergenceGroup;
    }

    private void setDuringTime(List<SystemEventWithText> alarmEvents) {
        for (SystemEventWithText systemEventWithText : alarmEvents) {
            if (Objects.nonNull(systemEventWithText.getEndTime())) {
                systemEventWithText.setDuration(systemEventWithText.getEndTime() - systemEventWithText.getEventTime());
            }
        }
    }

    private void addEventInfos(List<SystemEventWithText> alarmEvents, Map<BaseVo, Node> map, Long tenantId) {
        Map<Integer, String> energyTypeNameMap = projectEnergyTypeService.queryEnergyTypeNameMap(tenantId);
        Map<Integer, String> cycleMap = modelServiceUtils.getEnumByLabel(ModelLabelDef.AGGREGATION_CYCLE);
        for (SystemEventWithText event : alarmEvents) {
            Node node = map.get(new BaseVo(event.getObjectId(), event.getObjectLabel()));
            if (Objects.isNull(node)) {
                continue;
            }
            event.setLevelName(node.getLevelName());
            event.setName(node.getName());
            event.setEnergyTypeName(energyTypeNameMap.get(event.getEnergytype()));
            setSystemEventCycle(cycleMap, event);
        }
        //设置能效指标
        setEfInfo(alarmEvents);
    }

    private void setEfInfo(List<SystemEventWithText> alarmEvents) {
        List<SystemEventWithText> energyEfficiencyEvent = alarmEvents.stream()
                .filter(s -> s.getEventType().equals(EnumSystemEventType.ENERGY_EFFICIENCY_ALARM.getId()) || s.getEventType().equals(EnumSystemEventType.ENERGY_EFFICIENCY_ALARM_RETURN.getId()))
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(energyEfficiencyEvent)) {
            List<Long> efSetIds = energyEfficiencyEvent.stream().map(SystemEventWithText::getExtend).map(SystemEventExtendPo::getEfSetId).filter(Objects::nonNull).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(efSetIds)) {
                Map<Long, String> efSetIdNameMap = queryEnergyEfficiencyMap(efSetIds);
                for (SystemEventWithText systemEventWithText : energyEfficiencyEvent) {
                    String efSetName = efSetIdNameMap.get(systemEventWithText.getExtend().getEfSetId());
                    systemEventWithText.setEfSetName(efSetName);
                }
            }
        }
    }

    private Map<Long, String> queryEnergyEfficiencyMap(List<Long> efSetIds) {
        Map<Long, String> result = new HashMap<>();
        ParentQueryConditionBuilder builder = ParentQueryConditionBuilder.of(ModelLabelDef.ENERGY_EFFICIENCY_SET)
                .in(ColumnDef.ID, efSetIds);
        List<EnergyEfficiencySetDTO> energyEfficiencySetDTOS = modelServiceUtils.query(builder, EnergyEfficiencySetDTO.class);
        if (CollectionUtils.isEmpty(energyEfficiencySetDTOS)) {
            return result;
        }
        energyEfficiencySetDTOS.forEach(energyEfficiencySetDTO -> result.put(energyEfficiencySetDTO.getId(), energyEfficiencySetDTO.getName()));
        return result;
    }

    private EnergyEfficiencySetDTO queryEnergyEfficiencySet(Long efSetId) {
        ParentQueryConditionBuilder builder = ParentQueryConditionBuilder.of(ModelLabelDef.ENERGY_EFFICIENCY_SET)
                .eq(ColumnDef.ID, efSetId);
        List<EnergyEfficiencySetDTO> energyEfficiencySetDTOS = modelServiceUtils.query(builder, EnergyEfficiencySetDTO.class);
        if (CollectionUtils.isEmpty(energyEfficiencySetDTOS)) {
            log.error("can not found EnergyEfficiencySetDTO, id = {}", efSetId);
            return null;
        }
        return energyEfficiencySetDTOS.get(0);
    }

    private void setSystemEventCycle(Map<Integer, String> cycleMap, SystemEventWithText event) {
        SystemEventExtendPo extend = event.getExtend();
        if (extend == null) {
            return;
        }
        event.setCycle(extend.getCycle());
        event.setCycleName(cycleMap.get(event.getCycle()));
        event.setEfSetId(extend.getEfSetId());
    }

    private List<SystemEventWithText> queryEvents(EventParamQueryVO eventParam, List<EventNode> nodes, boolean onlyEquipment) {
        if (CollectionUtils.isEmpty(nodes)) {
            return Collections.emptyList();
        }

        Map<String, List<EventNode>> nodeMapByLabel = nodes.stream().collect(Collectors.groupingBy(EventNode::getModelLabel));
        List<SystemEventWithText> eventList = getQueryEventList(eventParam, nodes, onlyEquipment);

        // 仅查询收敛事件
        if (eventParam.isConvergence()) {
            List<SystemEventWithText> convergenceEvents = queryConvengenceEvent(eventList, eventParam, nodes, onlyEquipment);
            eventList.addAll(convergenceEvents);
        }

        List<EventNode> orDefault = nodeMapByLabel.getOrDefault(NodeLabelDef.MANU_EQUIPMENT, Collections.emptyList());
        Set<Long> manuIds = orDefault.stream().map(EventNode::getId).collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(manuIds)) {
            return eventList;
        }

        List<SystemEventWithText> otherEvents = eventList.stream().filter(item -> !Objects.equals(item.getObjectLabel(), NodeLabelDef.MANU_EQUIPMENT)).collect(Collectors.toList());
        List<SystemEventWithText> manuEvents = eventList.stream().filter(item -> Objects.equals(item.getObjectLabel(), NodeLabelDef.MANU_EQUIPMENT))
                .filter(item -> manuIds.contains(item.getObjectId())).collect(Collectors.toList());
        return Stream.of(otherEvents, manuEvents).filter(CollectionUtils::isNotEmpty).flatMap(Collection::stream).collect(Collectors.toList());
    }

    private List<SystemEventWithText> queryConvengenceEvent(List<SystemEventWithText> eventList, EventParamQueryVO eventParam, List<EventNode> nodes, boolean onlyEquipment) {
        if (CollectionUtils.isEmpty(eventList)) {
            return Collections.emptyList();
        }

        EventParamQueryVO newEventParam = new EventParamQueryVO();
        BeanUtils.copyProperties(eventParam, newEventParam);
        List<String> groupIds = eventList.stream()
                .filter(it -> StringUtils.isNotBlank(it.getConvergentGroup())).map(it -> it.getConvergentGroup()).distinct()
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(groupIds)) {
            return Collections.emptyList();
        }

        newEventParam.setGroupIds(groupIds);
        newEventParam.setConvergence(false);
        newEventParam.setIndex(null);
        newEventParam.setLimit(null);
        return getQueryEventList(newEventParam, nodes, onlyEquipment);
    }

    private List<SystemEventWithText> getQueryEventList(EventParamQueryVO eventParam, List<EventNode> nodes, boolean onlyEquipment) {
        Map<String, List<EventNode>> collect = nodes.stream().collect(Collectors.groupingBy(EventNode::getModelLabel));
        ParentQueryConditionBuilder builder = ParentQueryConditionBuilder.of(ModelLabelDef.SYSTEM_EVENT)
                .orderBy(ColumnDef.EVENT_TIME, false)
                .removeOrderById()
                .composeMethod(true);

        if (Objects.nonNull(eventParam.getLimit()) && Objects.nonNull(eventParam.getIndex())) {
            builder.limit(eventParam.getIndex(), eventParam.getLimit());
        }

        int group = 1;
        for (Map.Entry<String, List<EventNode>> entry : collect.entrySet()) {
            String modelLabel = entry.getKey();
            List<EventNode> tmpNodes = entry.getValue();

            // 对于非用能设备，或者是只有用能设备的情况直接按照用能设备的节点进行查询
            if (!Objects.equals(modelLabel, NodeLabelDef.MANU_EQUIPMENT) || onlyEquipment) {
                //查询的根节点是设备 直接使用设备id进行查询
                List<Long> nodeIds = tmpNodes.stream().map(EventNode::getId).distinct().collect(Collectors.toList());
                builder.eq(ColumnDef.OBJECT_Label, modelLabel, group)
                        .in(ColumnDef.OBJECT_ID, nodeIds, group);
                assembleQueryCondition(builder, eventParam, group++);
                continue;
            }

            // 用能设备既能挂room下面 又能挂其他层级下面
            // 查询的根节点不是设备 对于设备要使用roomId进行查询
            List<Long> roomIds = tmpNodes.stream().map(EventNode::getRoomId)
                    .filter(Objects::nonNull).distinct()
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(roomIds)) {
                builder.eq(ColumnDef.OBJECT_Label, modelLabel, group)
                        .in(ColumnDef.ROOM_ID, roomIds, group);
                assembleQueryCondition(builder, eventParam, group++);
            }

            // 用能设备挂在非room节点下面
            List<Long> manuIds = tmpNodes.stream()
                    .filter(item -> Objects.isNull(item.getRoomId()))
                    .map(EventNode::getId)
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(manuIds)) {
                builder.eq(ColumnDef.OBJECT_Label, modelLabel, group)
                        .in(ColumnDef.OBJECT_ID, manuIds, group);
                assembleQueryCondition(builder, eventParam, group++);
            }
        }

        return modelServiceUtils.query(builder.build(), SystemEventWithText.class);
    }

    private void assembleQueryCondition(ParentQueryConditionBuilder builder, EventParamQueryVO eventParam, int group) {
        builder.ge(ColumnDef.EVENT_TIME, eventParam.getStarttime(), group);
        builder.lt(ColumnDef.EVENT_TIME, eventParam.getEndtime(), group);
        builder.in(ColumnDef.EVENT_TYPE, eventParam.getTypes(), group);
        builder.in(ColumnDef.LEVEL, eventParam.getLevels(), group);

        if (StringUtils.isNotBlank(eventParam.getDescription())) {
            builder.like(ColumnDef.DESCRIPTION, eventParam.getDescription(), group);
        }

        if (ParamUtils.checkPrimaryKeyValid(eventParam.getStatus())) {
            builder.eq(CONFIRM_EVENT_STATUS, eventParam.getStatus(), group);
        }

        if (ParamUtils.checkPrimaryKeyValid(eventParam.getAlarmAggregationCycle())) {
            builder.eq(ColumnDef.AGGREGATION_CYCLE, eventParam.getAlarmAggregationCycle(), group);
        }

        // 对于收敛分组条件不为空的情况下，收敛状态条件一直生效
        // 对于收敛分组条件为空的情况下，只有查询收敛的时候才需要过滤
        if (CollectionUtils.isNotEmpty(eventParam.getGroupIds())) {
            builder.in(ColumnDef.CONVERGENT_GROUP, eventParam.getGroupIds(), group);
            builder.eq(ColumnDef.CONVERGENT_STATUS, eventParam.isConvergence(), group);
        } else {
            // 不查询收敛，则直接忽略该条件
            if (eventParam.isConvergence()) {
                builder.eq(ColumnDef.CONVERGENT_STATUS, eventParam.isConvergence(), group);
            }
        }
    }

    @Override
    public List<Map<String, Object>> confirmEvents(List<SystemEventConfirmRequest> systemEventConfirmRequest) {
        ApiResult<List<Map<String, Object>>> listResult = modelService.writeData(systemEventConfirmRequest);
        writeToRedis(systemEventConfirmRequest);
        return listResult.getData();
    }

    /**
     * 写入一条数据到redis，存储信息供查询
     *
     * @param systemEventConfirmRequest
     */
    private void writeToRedis(List<SystemEventConfirmRequest> systemEventConfirmRequest) {
        if (CollectionUtils.isEmpty(systemEventConfirmRequest)) {
            return;
        }

        for (SystemEventConfirmRequest event : systemEventConfirmRequest) {
            String key = event.getObjectId() + SplitCharDef.UNDERLINE + event.getObjectLabel() + SplitCharDef.UNDERLINE +
                    event.getEventTime() + SplitCharDef.UNDERLINE + event.getEventType() + SplitCharDef.UNDERLINE + event.getLevel();
            redisService.stringAddValue(RedisEventKey.EVENT_KEY_PREFIX + key, JsonTransferUtils.toJSONString(event.getConfirmeventstatus()), 1, TimeUnit.DAYS);
        }
    }

    @Override
    public void exportData(EventParamQueryVO eventParam, HttpServletResponse response, Long userId, Long tenantId) {
        // 最多只支持10000条数据导出
        eventParam.setIndex(0);
        eventParam.setLimit(10000);

        // 所有行的集合
        List<List<String>> rows = new ArrayList<>();
        //表头
        List<String> header = getExcelHeader(eventParam);
        //加入表格数据thbData.getCurrentdata()
        List<SystemEventWithText> eventVOS = queryHandleEvents(eventParam, false, userId, tenantId);
        getExcelData(rows, eventVOS, eventParam);

        String description = LanguageUtil.getMessage(ENERGY_INFO_ALARM_EVENT_DATA);
        Date date = new Date(System.currentTimeMillis());
        SimpleDateFormat formatter = new SimpleDateFormat("yyyyMMdd_HHmmss");
        String current = formatter.format(date);
        String excelName = description + current + ".xlsx";

        SimpleExcelSheetBuilder builder = SimpleExcelSheetBuilder.of(header, rows);
        List<Integer> colsWidth = Arrays.asList(16, 20, 16, 16, 85, 16, 25, 10, 10, 10);
        builder.setColsWidth(colsWidth);
        builder.download(response, excelName);

    }

    private void getExcelData(List<List<String>> rows, List<SystemEventWithText> eventVOS, EventParamQueryVO eventParam) {
        if (CollectionUtils.isEmpty(eventVOS)) {
            return;
        }

        int i = 0;
        ApiResult<List<IdTextPair>> confirmeventstatuss = modelService.getEnumrationByModel(CONFIRM_EVENT_STATUS);
        List<IdTextPair> status = confirmeventstatuss.getData();
        Map<Long, String> efIdNameMap = Collections.emptyMap();
        if (isEnergyEfficiencyEvent(eventParam.getTypes())) {
            Set<Long> ids = eventVOS.stream().map(SystemEventWithText::getExtend).map(SystemEventExtendPo::getEfSetId).filter(Objects::nonNull).collect(Collectors.toSet());
            efIdNameMap = queryEnergyEfficiencyMap(new ArrayList<>(ids));
        }
        Optional<IdTextPair> temp;
        for (SystemEventWithText alarmEventVO : eventVOS) {
            // 第 n 行的数据
            List<String> row = new ArrayList<>();
            i++;
            //序号
            row.add(String.valueOf(i));
            //节点层级
            row.add(alarmEventVO.getLevelName());
            //能源类型
            if (isEnergyConsumptionEvent(eventParam.getTypes())) {
                if (alarmEventVO.getEnergyTypeName() != null) {
                    row.add(alarmEventVO.getEnergyTypeName());
                } else {
                    row.add("--");
                }
            }
            if (isEnergyEfficiencyEvent(eventParam.getTypes())) {
                Long efSetId = alarmEventVO.getExtend().getEfSetId();
                if (Objects.isNull(efSetId) || MapUtils.isEmpty(efIdNameMap) || Objects.isNull(efIdNameMap.get(efSetId))) {
                    row.add("--");
                } else {
                    row.add(efIdNameMap.get(efSetId));
                }
            }
            //报警类型
            if (Objects.isNull(alarmEventVO.getExtend())) {
                row.add("--");
            } else if (Objects.isNull(alarmEventVO.getExtend().getCycle())) {
                row.add("--");
            } else {
                Integer cycle = alarmEventVO.getExtend().getCycle();
                if (cycle == 12) {
                    row.add(LanguageUtil.getMessage(ENERGY_INFO_DAILY_ALARM));
                } else if (cycle == 13) {
                    row.add(LanguageUtil.getMessage(ENERGY_INFO_WEEK_ALARM));
                } else if (cycle == 14) {
                    row.add(LanguageUtil.getMessage(ENERGY_INFO_MONTH_ALARM));
                } else if (cycle == 17) {
                    row.add(LanguageUtil.getMessage(ENERGY_INFO_ANNUAL_ALARM));
                } else {
                    row.add("--");
                }
            }
            //描述
            row.add(alarmEventVO.getDescription());
            //事件等级
            row.add(getLevelNameById(alarmEventVO.getLevel(), alarmEventVO.getEventType()));
            //发生时间
            if (Objects.isNull(alarmEventVO.getEventTime())) {
                row.add("--");
            } else {
                row.add(TimeUtil.format(alarmEventVO.getEventTime(), TimeFormatDef.SUPPER_LONG_TIME_FORMAT));
            }
            //结束时间
            if (Objects.isNull(alarmEventVO.getEndTime())) {
                row.add("--");
            } else {
                row.add(TimeUtil.format(alarmEventVO.getEndTime(), TimeFormatDef.SUPPER_LONG_TIME_FORMAT));
            }
            //持续时间
            row.add(getDistanceTime(alarmEventVO.getEventTime(), alarmEventVO.getEndTime()));
            //状态
            temp = status.stream().filter(x -> x.getId().equals(alarmEventVO.getConfirmEventStatus())).findFirst();
            if (temp.isPresent()) {
                if (Objects.equals(temp.get().getPropertyLabel(), EnumerationLangKeyDef.CONFIRMED)) {
                    row.add(LanguageUtil.getMessage(EnumerationLangKeyDef.PROCESSED));
                } else {
                    row.add(LanguageUtil.getMessage(EnumerationLangKeyDef.PENDING));
                }
            } else {
                row.add("--");
            }
            rows.add(row);
        }
    }

    public static String getDistanceTime(Long time1, Long time2) {
        if (time1 == null || time2 == null) {
            return "--";
        }

        long day = 0;
        long hour = 0;
        long min = 0;
        long sec = 0;
        long diff;

        if (time1 < time2) {
            diff = time2 - time1;
        } else {
            diff = time1 - time2;
        }
        day = diff / (24 * 60 * 60 * 1000);
        hour = (diff / (60 * 60 * 1000) - day * 24);
        min = ((diff / (60 * 1000)) - day * 24 * 60 - hour * 60);
        sec = (diff / 1000 - day * 24 * 60 * 60 - hour * 60 * 60 - min * 60);
        if (day != 0) {
            return day + LanguageUtil.getMessage(INFO_DAY) + hour + LanguageUtil.getMessage(INFO_HOUR) + min + LanguageUtil.getMessage(INFO_MINUTE) + sec + LanguageUtil.getMessage(INFO_SECOND);
        }
        if (hour != 0) {
            return hour + LanguageUtil.getMessage(INFO_HOUR) + min + LanguageUtil.getMessage(INFO_MINUTE) + sec + LanguageUtil.getMessage(INFO_SECOND);
        }
        if (min != 0) {
            return min + LanguageUtil.getMessage(INFO_MINUTE) + sec + LanguageUtil.getMessage(INFO_SECOND);
        }
        if (sec != 0) {
            return sec + LanguageUtil.getMessage(INFO_SECOND);
        }
        return 0 + LanguageUtil.getMessage(INFO_SECOND);
    }

    private String getLevelNameById(int level, int eventType) {
        if (Objects.equals(eventType, ALARM_TYPE)) {
            return LanguageUtil.getMessage(INFO_WARNING);
        }
        switch (level) {
            case 1:
                return LanguageUtil.getMessage(ENERGY_INFO_ALARM_LEVEL_ONE);
            case 2:
                return LanguageUtil.getMessage(ENERGY_INFO_ALARM_LEVEL_TWO);
            case 3:
                return LanguageUtil.getMessage(ENERGY_INFO_ALARM_LEVEL_THREE);
            case 4:
                return LanguageUtil.getMessage(ENERGY_INFO_ALARM_LEVEL_FOUR);
            default:
                return "--";
        }
    }

    private List<String> getExcelHeader(EventParamQueryVO eventParam) {
        List<String> header = new ArrayList<>();
        header.add(LanguageUtil.getMessage(ENERGY_ENTRY_INFO_EXPORT_SERIAL_NUMBER));
        header.add(LanguageUtil.getMessage(ENERGY_INFO_NODE_HIERARCHY));
        if (isEnergyConsumptionEvent(eventParam.getTypes())) {
            header.add(LanguageUtil.getMessage(ENERGY_INFO_ENERGY_TYPE));
        }
        if (isEnergyEfficiencyEvent(eventParam.getTypes())) {
            header.add(LanguageUtil.getMessage(ENERGY_INFO_INDICATOR_TYPE));
        }
        header.add(LanguageUtil.getMessage(ENERGY_INFO_ALARM_TYPE));
        header.add(LanguageUtil.getMessage(ENERGY_INFO_DESCRIBE));
        header.add(LanguageUtil.getMessage(ENERGY_INFO_ALARM_LEVEL));
        header.add(LanguageUtil.getMessage(ENERGY_INFO_ALARM_START_TIME));
        header.add(LanguageUtil.getMessage(ENERGY_INFO_ALARM_END_TIME));
        header.add(LanguageUtil.getMessage(ENERGY_INFO_ALARM_DURATION));
        header.add(LanguageUtil.getMessage(ENERGY_INFO_ALARM_STATE));

        return header;
    }

    private boolean isEnergyConsumptionEvent(List<Integer> types) {
        return types.contains(EnumSystemEventType.ENERGY_CONSUMPTION_WARNING.getId()) || types.contains(EnumSystemEventType.ENERGY_CONSUMPTION_ALARM.getId()) || types.contains(EnumSystemEventType.ENERGY_CONSUMPTION_ALARM_RETURN.getId());
    }

    private boolean isEnergyEfficiencyEvent(List<Integer> type) {
        return type.contains(EnumSystemEventType.ENERGY_EFFICIENCY_ALARM.getId()) || type.contains(EnumSystemEventType.ENERGY_EFFICIENCY_ALARM_RETURN.getId());
    }

    private void handleEventParam(EventParamQueryVO eventParam) {
        if (CollectionUtils.isNotEmpty(eventParam.getTypes()) && eventParam.getTypes().contains(energyconsumpdearlyalarm)) {
            if (CollectionUtils.isNotEmpty(eventParam.getLevels())) {
                eventParam.getLevels().add(alarmReturnLevel);
            } else {
                List<Integer> levels = new ArrayList<>();
                levels.add(alarmReturnLevel);
                eventParam.setLevels(levels);
            }
        }

        eventParam.setStarttime(eventParam.getStarttime() - systemEventOffset);
        eventParam.setEndtime(eventParam.getEndtime() + systemEventOffset);
    }

    /**
     * 获取所有节点 将前端传入节点树 展开为单个节点
     *
     * @param eventParam
     * @return
     */
    private List<EventNode> getAllNodes(EventParamQueryVO eventParam) {
        List<EventNode> nodes = getNodesFromTree(eventParam.getChildren(), null, null);
        EventNode baseVo = new EventNode((long) eventParam.getId(), eventParam.getModelLabel(), eventParam.getName(), null, eventParam.getName());
        return Stream.of(nodes, Collections.singletonList(baseVo)).filter(CollectionUtils::isNotEmpty).flatMap(Collection::stream).collect(Collectors.toList());
    }

    private List<EventNode> getNodesFromTree(List<Node> tree, Long roomId, String allParentName) {
        if (CollectionUtils.isEmpty(tree)) {
            return Collections.emptyList();
        }
        return tree.stream().map(node -> {
            String tmpName = Stream.of(allParentName, node.getName()).filter(StringUtils::isNotEmpty).collect(Collectors.joining(">"));
            EventNode baseVo = EventNode.builder()
                    .id(node.getNodeId())
                    .modelLabel(node.getModelLabel())
                    .name(node.getName())
                    .roomId(roomId)
                    .levelName(tmpName)
                    .build();
            Long tmpRoomId = roomId;
            if (Objects.equals(node.getModelLabel(), NodeLabelDef.ROOM)) {
                tmpRoomId = node.getNodeId();
            }
            List<EventNode> children = getNodesFromTree(node.getChildren(), tmpRoomId, tmpName);
            return Stream.of(Collections.singletonList(baseVo), children).filter(CollectionUtils::isNotEmpty).flatMap(Collection::stream).collect(Collectors.toList());
        }).flatMap(Collection::stream).collect(Collectors.toList());
    }

    @Override
    public EventAnalysisResult queryEfEventAnalysis(EventAnalysisRequestParam param, Long userId) {
        EventAnalysisResult result = new EventAnalysisResult();
        List<SystemEvent> systemEvents = systemEventService.querySystemEventList(Collections.singletonList(param.getEventId()));
        //1.对事件必要参数进行校验
        if (CollectionUtils.isEmpty(systemEvents) || Objects.isNull(systemEvents.get(0).getExtend()) || Objects.isNull(systemEvents.get(0).getExtend().getCycle())
            || Objects.isNull(systemEvents.get(0).getExtend().getEfSetId())) {
            return result;
        }
        SystemEvent systemEventVo = systemEvents.get(0);
        EnergyEfficiencySetDTO energyEfficiencySetDTO = queryEnergyEfficiencySet(systemEventVo.getExtend().getEfSetId());
        if (energyEfficiencySetDTO == null) {
            return result;
        }
        //2.获取实际数据(能耗/能效)
        List<DataLogData> dataLogDataList = queryActual(param, systemEventVo.getExtend(), new BaseEntity(energyEfficiencySetDTO.getRootnodeid(), energyEfficiencySetDTO.getRootnodelabel()));
        result.setActual(dataLogDataList);

        //4.设置(能效/能耗)定额
        result.setLimit(Objects.isNull(systemEventVo.getExtend().getTest()) ? systemEventVo.getExtend().getValue() : systemEventVo.getExtend().getTest());
        //5.获取报警线
        result.setThreshold(getAlarmThresholdLines(systemEventVo.getExtend().getAlarmSchemeId(), result.getLimit()));
        //6.设置单位
        result.setSymbol(energyEfficiencySetDTO.getSymbol());
        return result;
    }

    public List<DataLogData> queryActual(EventAnalysisRequestParam param, SystemEventExtendDTO extend, BaseEntity rootNode) {
        StartEndTime startEndTime = generatorStartEndTimeWithUnNaturalCycle(param, extend, rootNode);
        long startTime = DateUtil.localDateTime2EpochMilli(startEndTime.getStartTime());
        long endTime = DateUtil.localDateTime2EpochMilli(startEndTime.getEndTime());
        List<Long> times = TimeUtil.getTimeRange(startTime, endTime, extend.getCycle());
        List<EnergyEfficiencyDataDTO> efDataList = queryEfData(extend.getEfSetId(), startTime, endTime, param.getId(), param.getModelLabel(), extend.getCycle());
        Map<Long, Double> dataMap = efDataList.stream().collect(Collectors.toMap(EnergyEfficiencyDataDTO::getLogtime, EnergyEfficiencyDataDTO::getValue, (v1, v2) -> v1));
        List<DataLogData> actual = new ArrayList<>(dataMap.size());
        for (Long time : times) {
            Double value = dataMap.get(time);
            DataLogData record = new DataLogData();
            actual.add(record);
            record.setTime(time);
            record.setValue(value);
        }
        return actual;
    }

    public List<EnergyEfficiencyDataDTO> queryEfData(Long efSetId, long st, long et, long nodeId, String nodeLabel, int cycle) {
        if (efSetId == null) {
            return Collections.emptyList();
        }
        QueryCondition build = ParentQueryConditionBuilder.of(ModelLabelDef.ENERGY_EFFICIENCY_DATA)
                .where(ColumnDef.ENERGY_EFFICIENCY_SET_ID, ConditionBlock.OPERATOR_EQ, efSetId)
                .where(ColumnDef.LOG_TIME, ConditionBlock.OPERATOR_GE, st)
                .where(ColumnDef.LOG_TIME, ConditionBlock.OPERATOR_LT, et)
                .where(ColumnDef.C_OBJECT_Label, ConditionBlock.OPERATOR_EQ, nodeLabel)
                .where(ColumnDef.C_OBJECT_ID, ConditionBlock.OPERATOR_EQ, nodeId)
                .where(ColumnDef.AGGREGATION_CYCLE, ConditionBlock.OPERATOR_EQ, cycle)
                .build();

        return modelServiceUtils.query(build, EnergyEfficiencyDataDTO.class);
    }

    private StartEndTime generatorStartEndTimeWithUnNaturalCycle(EventAnalysisRequestParam param, SystemEventExtendDTO extend, BaseEntity rootNode){
        if(Objects.equals(extend.getCycle(), AggregationCycle.ONE_YEAR)){
            return generatorStartEndTime(param,extend);
        }
        Integer unNaturalCycle = getUnNaturalCycle(extend.getCycle());
        UnnaturalSetVo unnaturalSetVo = unnaturalTimeService.getUnnaturalSetVo(rootNode, unNaturalCycle);
        return  unnaturalTimeService.getBelongTimeRange(unNaturalCycle, TimeUtil.timestamp2LocalDateTime(param.getStarttime()), unnaturalSetVo);
    }

    public StartEndTime generatorStartEndTime(EventAnalysisRequestParam param, SystemEventExtendDTO extend) {
        LocalDateTime eventStartTime = DateUtil.timeStamp2LocalDateTime(param.getStarttime());
        StartEndTime currentPeriod = null;
        LocalDate endTime = TimeUtil.getFirstDayOfNextYear(eventStartTime.toLocalDate());
        LocalDate startTime = endTime.minusYears(4);
        currentPeriod = new StartEndTime(LocalDateTime.of(startTime, LocalTime.MIN), LocalDateTime.of(endTime, LocalTime.MIN));
        return currentPeriod;
    }

    private Integer getUnNaturalCycle(Integer aggregationCycle){
        if(Objects.equals(aggregationCycle,AggregationCycle.ONE_HOUR)){
            return AggregationCycle.ONE_DAY;
        }
        if(Objects.equals(aggregationCycle,AggregationCycle.ONE_DAY)){
            return AggregationCycle.ONE_MONTH;
        }
        if(Objects.equals(aggregationCycle,AggregationCycle.SEVEN_DAYS)){
            return AggregationCycle.SEVEN_DAYS;
        }
        if(Objects.equals(aggregationCycle,AggregationCycle.ONE_MONTH)){
            return AggregationCycle.ONE_YEAR;
        }
        return AggregationCycle.ONE_YEAR;
    }

    /**
     * 获取阈值的报警线信息
     *
     * @param alarmSchemeId alarmScheme主键ID
     * @return 阈值的报警线信息
     */
    private List<AlarmThresholdWithLevel> getAlarmThresholdLines(Long alarmSchemeId, Double value) {
        if (Objects.isNull(alarmSchemeId) || Objects.isNull(value)) {
            return null;
        }
        EEMAlarmScheme eemAlarmScheme = alarmSchemeDao.selectRelatedById(EEMAlarmScheme.class, alarmSchemeId, Collections.singletonList(QueryWrapper.of(EEMAlarmLevel.class)));
        if (Objects.isNull(eemAlarmScheme) || CollectionUtils.isEmpty(eemAlarmScheme.getAlarmlevelconfig_model())) {
            return Collections.emptyList();
        }
        List<EEMAlarmLevel> alarmLevelConfig = eemAlarmScheme.getAlarmlevelconfig_model();
        List<AlarmThresholdWithLevel> thresholdWithLevelList = new ArrayList<>(alarmLevelConfig.size());
        for (EEMAlarmLevel eemAlarmLevel : alarmLevelConfig) {
            AlarmThresholdWithLevel alarmThresholdWithLevel = new AlarmThresholdWithLevel();
            if (Objects.isNull(eemAlarmLevel.getAlarmcolorset_id()) || Objects.isNull(eemAlarmLevel.getRate())) {
                continue;
            }
            alarmThresholdWithLevel.setLevel(eemAlarmLevel.getAlarmcolorset_id());
            alarmThresholdWithLevel.setValue(value * eemAlarmLevel.getRate() / 100);
            thresholdWithLevelList.add(alarmThresholdWithLevel);
        }
        return thresholdWithLevelList;
    }



}
