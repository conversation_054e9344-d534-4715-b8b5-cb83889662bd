package com.aicodereview.service;

import com.aicodereview.model.TaskStatus;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CompletableFuture;

/**
 * 代码评审服务
 */
@Service
public class CodeReviewService {

    private static final Logger logger = LoggerFactory.getLogger(CodeReviewService.class);

    private final DeepSeekService deepSeekService;
    private final PromptService promptService;
    private final TaskStatusService taskStatusService;
    private final FileService fileService;
    private final ObjectMapper objectMapper;

    @Autowired
    public CodeReviewService(DeepSeekService deepSeekService, 
                           PromptService promptService,
                           TaskStatusService taskStatusService,
                           FileService fileService,
                           ObjectMapper objectMapper) {
        this.deepSeekService = deepSeekService;
        this.promptService = promptService;
        this.taskStatusService = taskStatusService;
        this.fileService = fileService;
        this.objectMapper = objectMapper;
    }

    /**
     * 异步执行代码评审任务
     */
    @Async("codeReviewTaskExecutor")
    public CompletableFuture<Void> reviewCodeAsync(List<FileInfo> files, String resultDir, String customQuestion) {
        logger.info("开始异步代码评审任务: {}, 文件数: {}", resultDir, files.size());
        
        try {
            // 初始化任务状态
            taskStatusService.startTask(resultDir, files.size());

            // 获取提示词模板
            List<PromptService.PromptTemplate> templates = StringUtils.hasText(customQuestion) 
                ? promptService.createCustomPromptTemplate(customQuestion)
                : promptService.loadPromptTemplates();

            // 处理每个文件
            for (FileInfo fileInfo : files) {
                try {
                    processFile(fileInfo, resultDir, templates);
                    taskStatusService.incrementProgress(resultDir);
                } catch (Exception e) {
                    String error = String.format("文件 %s 评审失败: %s", fileInfo.getFilename(), e.getMessage());
                    logger.error(error, e);
                    taskStatusService.markFailed(resultDir, error);
                    return CompletableFuture.completedFuture(null);
                }
            }

            // 生成汇总报告
            generateSummaryReport(resultDir, files);
            
            // 标记任务完成
            taskStatusService.markCompleted(resultDir);
            logger.info("代码评审任务完成: {}", resultDir);

        } catch (Exception e) {
            String error = "代码评审任务执行失败: " + e.getMessage();
            logger.error(error, e);
            taskStatusService.markFailed(resultDir, error);
        }

        return CompletableFuture.completedFuture(null);
    }

    /**
     * 处理单个文件
     */
    private void processFile(FileInfo fileInfo, String resultDir, List<PromptService.PromptTemplate> templates) 
            throws IOException {
        logger.debug("开始处理文件: {}", fileInfo.getFilename());
        
        // 更新当前处理文件
        taskStatusService.updateStatus(resultDir, TaskStatus.Status.PROCESSING, fileInfo.getFilename(), null);

        // 读取文件内容
        String codeContent = Files.readString(Paths.get(fileInfo.getPath()));

        // 对每个提示词模板进行评审
        List<ReviewResult> results = new ArrayList<>();
        for (PromptService.PromptTemplate template : templates) {
            try {
                String reviewResult = deepSeekService.reviewCode(codeContent, template.getPromptContent());
                results.add(new ReviewResult(template.getRuleName(), reviewResult));
                
                // 保存单个评审结果
                fileService.saveReviewResult(resultDir, fileInfo, template.getRuleName(), reviewResult);
                
            } catch (Exception e) {
                logger.warn("评审规则 {} 执行失败: {}", template.getRuleName(), e.getMessage());
                results.add(new ReviewResult(template.getRuleName(), "评审过程发生错误: " + e.getMessage()));
            }
        }

        logger.debug("文件 {} 评审完成，生成了 {} 个评审结果", fileInfo.getFilename(), results.size());
    }

    /**
     * 生成汇总报告
     */
    private void generateSummaryReport(String resultDir, List<FileInfo> files) {
        try {
            logger.debug("开始生成汇总报告: {}", resultDir);
            
            // 读取所有评审结果
            List<ReviewResult> allResults = new ArrayList<>();
            for (FileInfo fileInfo : files) {
                // 这里可以读取之前保存的评审结果，或者重新生成
                // 为简化，这里生成一个基本的汇总
            }

            // 生成汇总JSON
            SummaryReport summary = new SummaryReport(files.size(), allResults, "代码评审完成");
            String summaryJson = objectMapper.writeValueAsString(summary);
            
            // 保存汇总报告
            fileService.saveSummaryReport(resultDir, summaryJson);
            
            logger.debug("汇总报告生成完成: {}", resultDir);
            
        } catch (Exception e) {
            logger.error("生成汇总报告失败: {}", resultDir, e);
        }
    }

    /**
     * 文件信息类
     */
    public static class FileInfo {
        private final String filename;
        private final String path;

        public FileInfo(String filename, String path) {
            this.filename = filename;
            this.path = path;
        }

        public String getFilename() {
            return filename;
        }

        public String getPath() {
            return path;
        }
    }

    /**
     * 评审结果类
     */
    public static class ReviewResult {
        private final String ruleName;
        private final String reviewResult;

        public ReviewResult(String ruleName, String reviewResult) {
            this.ruleName = ruleName;
            this.reviewResult = reviewResult;
        }

        public String getRuleName() {
            return ruleName;
        }

        public String getReviewResult() {
            return reviewResult;
        }
    }

    /**
     * 汇总报告类
     */
    public static class SummaryReport {
        private final int totalFiles;
        private final List<ReviewResult> results;
        private final String overallSummary;

        public SummaryReport(int totalFiles, List<ReviewResult> results, String overallSummary) {
            this.totalFiles = totalFiles;
            this.results = results;
            this.overallSummary = overallSummary;
        }

        public int getTotalFiles() {
            return totalFiles;
        }

        public List<ReviewResult> getResults() {
            return results;
        }

        public String getOverallSummary() {
            return overallSummary;
        }
    }
}
