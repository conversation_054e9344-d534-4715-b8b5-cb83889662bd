package com.aicodereview.service;

import com.aicodereview.model.TaskStatus;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CompletableFuture;

/**
 * 代码评审服务
 */
@Service
public class CodeReviewService {

    private static final Logger logger = LoggerFactory.getLogger(CodeReviewService.class);

    private final DeepSeekService deepSeekService;
    private final PromptService promptService;
    private final TaskStatusService taskStatusService;
    private final FileService fileService;
    private final ObjectMapper objectMapper;

    @Autowired
    public CodeReviewService(DeepSeekService deepSeekService, 
                           PromptService promptService,
                           TaskStatusService taskStatusService,
                           FileService fileService,
                           ObjectMapper objectMapper) {
        this.deepSeekService = deepSeekService;
        this.promptService = promptService;
        this.taskStatusService = taskStatusService;
        this.fileService = fileService;
        this.objectMapper = objectMapper;
    }

    /**
     * 异步执行代码评审任务
     */
    @Async("codeReviewTaskExecutor")
    public CompletableFuture<Void> reviewCodeAsync(List<FileInfo> files, String resultDir, String customQuestion) {
        logger.info("开始异步代码评审任务: {}, 文件数: {}", resultDir, files.size());
        
        try {
            // 初始化任务状态
            taskStatusService.startTask(resultDir, files.size());

            // 获取提示词模板
            List<PromptService.PromptTemplate> templates = StringUtils.hasText(customQuestion) 
                ? promptService.createCustomPromptTemplate(customQuestion)
                : promptService.loadPromptTemplates();

            // 处理每个文件
            for (FileInfo fileInfo : files) {
                try {
                    processFile(fileInfo, resultDir, templates);
                    taskStatusService.incrementProgress(resultDir);
                } catch (Exception e) {
                    String error = String.format("文件 %s 评审失败: %s", fileInfo.getFilename(), e.getMessage());
                    logger.error(error, e);
                    taskStatusService.markFailed(resultDir, error);
                    return CompletableFuture.completedFuture(null);
                }
            }

            // 生成汇总报告
            generateSummaryReport(resultDir, files);
            
            // 标记任务完成
            taskStatusService.markCompleted(resultDir);
            logger.info("代码评审任务完成: {}", resultDir);

        } catch (Exception e) {
            String error = "代码评审任务执行失败: " + e.getMessage();
            logger.error(error, e);
            taskStatusService.markFailed(resultDir, error);
        }

        return CompletableFuture.completedFuture(null);
    }

    /**
     * 处理单个文件
     */
    private void processFile(FileInfo fileInfo, String resultDir, List<PromptService.PromptTemplate> templates) 
            throws IOException {
        logger.debug("开始处理文件: {}", fileInfo.getFilename());
        
        // 更新当前处理文件
        taskStatusService.updateStatus(resultDir, TaskStatus.Status.PROCESSING, fileInfo.getFilename(), null);

        // 读取文件内容
        String codeContent = Files.readString(Paths.get(fileInfo.getPath()));

        // 对每个提示词模板进行评审
        List<ReviewResult> results = new ArrayList<>();
        for (PromptService.PromptTemplate template : templates) {
            try {
                String reviewResult = deepSeekService.reviewCode(codeContent, template.getPromptContent());
                results.add(new ReviewResult(template.getRuleName(), reviewResult));
                
                // 保存单个评审结果
                fileService.saveReviewResult(resultDir, fileInfo, template.getRuleName(), reviewResult);
                
            } catch (Exception e) {
                logger.warn("评审规则 {} 执行失败: {}", template.getRuleName(), e.getMessage());
                results.add(new ReviewResult(template.getRuleName(), "评审过程发生错误: " + e.getMessage()));
            }
        }

        logger.debug("文件 {} 评审完成，生成了 {} 个评审结果", fileInfo.getFilename(), results.size());
    }

    /**
     * 生成汇总报告
     */
    private void generateSummaryReport(String resultDir, List<FileInfo> files) {
        try {
            logger.debug("开始生成汇总报告: {}", resultDir);

            // 读取所有评审结果
            List<ReviewResult> allResults = new ArrayList<>();
            for (FileInfo fileInfo : files) {
                // 读取该文件的所有评审结果
                List<ReviewResult> fileResults = readFileReviewResults(resultDir, fileInfo);
                allResults.addAll(fileResults);
            }

            // 使用AI汇总评审结果生成标准化报告
            StandardReviewReport standardReport = generateStandardReportWithAI(resultDir, files, allResults);
            String standardJson = objectMapper.writeValueAsString(standardReport);

            // 保存标准化汇总报告（覆盖原有的简单格式）
            fileService.saveSummaryReport(resultDir, standardJson);

            logger.debug("汇总报告生成完成: {} 个问题", standardReport.getTotalProblems());

        } catch (Exception e) {
            logger.error("生成汇总报告失败: {}", resultDir, e);
        }
    }

    /**
     * 读取单个文件的所有评审结果
     */
    private List<ReviewResult> readFileReviewResults(String resultDir, FileInfo fileInfo) {
        List<ReviewResult> results = new ArrayList<>();
        try {
            String baseFileName = fileInfo.getFilename().replace(".java", "");
            String[] ruleNames = {"bug_detection", "performance", "security", "maintainability", "code_structure", "best_practices"};

            for (String ruleName : ruleNames) {
                try {
                    String fileName = baseFileName + "_" + ruleName + "_review.md";
                    String content = fileService.readFileContent(resultDir, fileName);

                    // 过滤掉错误信息
                    if (!content.contains("评审过程发生错误") && !content.trim().isEmpty()) {
                        results.add(new ReviewResult(ruleName, content));
                    }
                } catch (Exception e) {
                    logger.debug("读取评审文件失败: {}_{}_review.md", baseFileName, ruleName);
                }
            }
        } catch (Exception e) {
            logger.warn("读取文件评审结果失败: {}", fileInfo.getFilename(), e);
        }
        return results;
    }

    /**
     * 使用AI生成标准化评审报告
     */
    private StandardReviewReport generateStandardReportWithAI(String resultDir, List<FileInfo> files, List<ReviewResult> allResults) {
        try {
            if (allResults.isEmpty()) {
                logger.warn("没有有效的评审结果，生成空报告");
                return new StandardReviewReport(0, new ArrayList<>());
            }

            // 构建AI汇总提示词
            String summaryPrompt = buildAISummaryPrompt(files, allResults);

            // 调用AI进行汇总
            String aiSummary = deepSeekService.reviewCode("", summaryPrompt);

            // 解析AI返回的结果
            return parseAISummaryToStandardReport(aiSummary);

        } catch (Exception e) {
            logger.error("AI汇总评审结果失败", e);
            return new StandardReviewReport(0, new ArrayList<>());
        }
    }

    /**
     * 构建AI汇总提示词
     */
    private String buildAISummaryPrompt(List<FileInfo> files, List<ReviewResult> allResults) {
        StringBuilder prompt = new StringBuilder();

        prompt.append("请分析以下代码评审结果，并将其汇总成标准化的问题列表。\n\n");
        prompt.append("文件信息：\n");
        for (FileInfo file : files) {
            prompt.append("- ").append(file.getFilename()).append("\n");
        }

        prompt.append("\n评审结果：\n");
        for (ReviewResult result : allResults) {
            prompt.append("=== ").append(result.getRuleName()).append(" 评审结果 ===\n");
            prompt.append(result.getReviewResult()).append("\n\n");
        }

        prompt.append("请按照以下JSON格式输出汇总结果，只输出JSON，不要其他内容：\n");
        prompt.append("{\n");
        prompt.append("  \"total_problems\": 问题总数,\n");
        prompt.append("  \"problems\": [\n");
        prompt.append("    {\n");
        prompt.append("      \"problem_id\": 问题编号(从1开始),\n");
        prompt.append("      \"location\": \"文件名:行号\",\n");
        prompt.append("      \"description\": \"问题描述\",\n");
        prompt.append("      \"severity\": \"严重程度(高/中/低)\",\n");
        prompt.append("      \"suggestion\": \"改进建议\"\n");
        prompt.append("    }\n");
        prompt.append("  ]\n");
        prompt.append("}\n\n");
        prompt.append("注意：\n");
        prompt.append("1. 只提取真正的代码问题，忽略一般性建议\n");
        prompt.append("2. 合并相似的问题\n");
        prompt.append("3. 如果无法确定具体行号，使用文件名\n");
        prompt.append("4. 严重程度分为：高(安全问题、严重bug)、中(性能问题、设计缺陷)、低(代码风格、轻微改进)\n");

        return prompt.toString();
    }

    /**
     * 解析AI返回的汇总结果
     */
    private StandardReviewReport parseAISummaryToStandardReport(String aiSummary) {
        try {
            // 提取JSON部分
            String jsonContent = extractJsonFromResponse(aiSummary);
            if (jsonContent == null) {
                logger.warn("无法从AI响应中提取JSON内容");
                return new StandardReviewReport(0, new ArrayList<>());
            }

            // 解析JSON
            StandardReviewReport report = objectMapper.readValue(jsonContent, StandardReviewReport.class);
            return report;

        } catch (Exception e) {
            logger.error("解析AI汇总结果失败: {}", aiSummary, e);

            // 如果JSON解析失败，尝试简单的文本解析
            return parseAISummaryAsText(aiSummary);
        }
    }

    /**
     * 从AI响应中提取JSON内容
     */
    private String extractJsonFromResponse(String response) {
        // 查找JSON开始和结束标记
        int startIndex = response.indexOf("{");
        int endIndex = response.lastIndexOf("}");

        if (startIndex != -1 && endIndex != -1 && endIndex > startIndex) {
            return response.substring(startIndex, endIndex + 1);
        }

        return null;
    }

    /**
     * 当JSON解析失败时，尝试文本解析
     */
    private StandardReviewReport parseAISummaryAsText(String aiSummary) {
        List<ReviewProblem> problems = new ArrayList<>();

        // 简单的文本解析逻辑
        String[] lines = aiSummary.split("\n");
        int problemId = 1;

        for (String line : lines) {
            line = line.trim();
            if (line.contains("问题") || line.contains("建议") || line.contains("改进")) {
                problems.add(new ReviewProblem(
                    problemId++,
                    "未知位置",
                    line,
                    "中",
                    "请参考评审建议进行改进"
                ));
            }
        }

        return new StandardReviewReport(problems.size(), problems);
    }

    /**
     * 文件信息类
     */
    public static class FileInfo {
        private final String filename;
        private final String path;

        public FileInfo(String filename, String path) {
            this.filename = filename;
            this.path = path;
        }

        public String getFilename() {
            return filename;
        }

        public String getPath() {
            return path;
        }
    }

    /**
     * 评审结果类
     */
    public static class ReviewResult {
        private final String ruleName;
        private final String reviewResult;

        public ReviewResult(String ruleName, String reviewResult) {
            this.ruleName = ruleName;
            this.reviewResult = reviewResult;
        }

        public String getRuleName() {
            return ruleName;
        }

        public String getReviewResult() {
            return reviewResult;
        }
    }

    /**
     * 汇总报告类
     */
    public static class SummaryReport {
        private final int totalFiles;
        private final List<ReviewResult> results;
        private final String overallSummary;

        public SummaryReport(int totalFiles, List<ReviewResult> results, String overallSummary) {
            this.totalFiles = totalFiles;
            this.results = results;
            this.overallSummary = overallSummary;
        }

        public int getTotalFiles() {
            return totalFiles;
        }

        public List<ReviewResult> getResults() {
            return results;
        }

        public String getOverallSummary() {
            return overallSummary;
        }
    }
}
