package com.aicodereview.service;

import com.aicodereview.service.CodeReviewService.FileInfo;
import com.aicodereview.service.CodeReviewService.ReviewProblem;
import com.aicodereview.service.CodeReviewService.StandardReviewReport;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class ReviewSummaryServiceTest {

    @Mock
    private DeepSeekService deepSeekService;

    private ReviewSummaryService reviewSummaryService;
    private ObjectMapper objectMapper;
    private String testResultDir;

    @BeforeEach
    void setUp() throws IOException {
        objectMapper = new ObjectMapper();
        reviewSummaryService = new ReviewSummaryService(deepSeekService, objectMapper);
        
        // 创建测试目录
        testResultDir = "test_review_" + System.currentTimeMillis();
        Path testPath = Paths.get("results", testResultDir);
        Files.createDirectories(testPath);
        
        // 创建测试评审文件
        createTestReviewFiles(testPath);
    }

    private void createTestReviewFiles(Path testPath) throws IOException {
        // 创建包含问题的评审文件
        String bugReview = """
            ## Bug检测评审结果
            
            发现以下问题：
            
            1. 在AiPlcControlServiceV2Impl.java:122行，遥调失败重试时使用了ScheduledExecutorService，但未处理重试过程中的异常，可能导致线程泄漏或任务堆积。
            
            2. 缺少对空指针的检查，可能导致NullPointerException。
            
            建议：
            - 在重试任务内部添加异常捕获
            - 限制重试次数，避免无限重试
            - 添加空指针检查
            """;
            
        String performanceReview = """
            ## 性能评审结果
            
            发现以下性能问题：
            
            1. 数据库查询未使用索引，可能导致性能问题
            2. 循环中存在重复计算
            
            建议优化查询语句和算法逻辑。
            """;
            
        Files.writeString(testPath.resolve("TestFile_bug_detection_review.md"), bugReview);
        Files.writeString(testPath.resolve("TestFile_performance_review.md"), performanceReview);
    }

    @Test
    void testGenerateStandardReport() throws Exception {
        // 准备测试数据
        List<FileInfo> files = Arrays.asList(
            new FileInfo("AiPlcControlServiceV2Impl.java", "/path/to/file")
        );

        // 模拟AI返回的汇总结果
        String mockAiResponse = """
            {
              "total_problems": 2,
              "problems": [
                {
                  "problem_id": 1,
                  "location": "AiPlcControlServiceV2Impl.java:122",
                  "description": "在遥调失败重试时使用了ScheduledExecutorService，但未处理重试过程中的异常，可能导致线程泄漏或任务堆积",
                  "severity": "高",
                  "suggestion": "在重试任务内部添加异常捕获，确保任务异常不会导致线程终止；同时应限制重试次数，避免无限重试。"
                },
                {
                  "problem_id": 2,
                  "location": "AiPlcControlServiceV2Impl.java",
                  "description": "数据库查询未使用索引，可能导致性能问题",
                  "severity": "中",
                  "suggestion": "优化查询语句，添加适当的数据库索引。"
                }
              ]
            }
            """;

        when(deepSeekService.reviewCode(eq(""), anyString())).thenReturn(mockAiResponse);

        // 执行测试
        StandardReviewReport report = reviewSummaryService.generateStandardReport(testResultDir, files);

        // 验证结果
        assertNotNull(report);
        assertEquals(2, report.getTotalProblems());
        assertEquals(2, report.getProblems().size());

        ReviewProblem firstProblem = report.getProblems().get(0);
        assertEquals(1, firstProblem.getProblemId());
        assertEquals("AiPlcControlServiceV2Impl.java:122", firstProblem.getLocation());
        assertEquals("高", firstProblem.getSeverity());
        assertTrue(firstProblem.getDescription().contains("ScheduledExecutorService"));
    }

    @Test
    void testSaveStandardReport() throws Exception {
        // 准备测试数据
        ReviewProblem problem = new ReviewProblem(
            1,
            "TestFile.java:10",
            "测试问题描述",
            "中",
            "测试建议"
        );
        
        StandardReviewReport report = new StandardReviewReport(1, Arrays.asList(problem));

        // 执行保存
        reviewSummaryService.saveStandardReport(testResultDir, report);

        // 验证文件是否创建
        Path reportFile = Paths.get("results", testResultDir, "review.json");
        assertTrue(Files.exists(reportFile));

        // 验证文件内容
        String content = Files.readString(reportFile);
        assertTrue(content.contains("total_problems"));
        assertTrue(content.contains("problems"));
        assertTrue(content.contains("TestFile.java:10"));
    }

    @Test
    void testJsonSerialization() throws Exception {
        // 测试JSON序列化和反序列化
        ReviewProblem problem = new ReviewProblem(
            1,
            "TestFile.java:10",
            "测试问题",
            "高",
            "测试建议"
        );
        
        StandardReviewReport originalReport = new StandardReviewReport(1, Arrays.asList(problem));

        // 序列化
        String json = objectMapper.writeValueAsString(originalReport);
        
        // 验证JSON格式
        assertTrue(json.contains("\"total_problems\":1"));
        assertTrue(json.contains("\"problem_id\":1"));
        assertTrue(json.contains("\"location\":\"TestFile.java:10\""));

        // 反序列化
        StandardReviewReport deserializedReport = objectMapper.readValue(json, StandardReviewReport.class);
        
        // 验证反序列化结果
        assertEquals(originalReport.getTotalProblems(), deserializedReport.getTotalProblems());
        assertEquals(originalReport.getProblems().size(), deserializedReport.getProblems().size());
        
        ReviewProblem deserializedProblem = deserializedReport.getProblems().get(0);
        assertEquals(problem.getProblemId(), deserializedProblem.getProblemId());
        assertEquals(problem.getLocation(), deserializedProblem.getLocation());
        assertEquals(problem.getDescription(), deserializedProblem.getDescription());
        assertEquals(problem.getSeverity(), deserializedProblem.getSeverity());
        assertEquals(problem.getSuggestion(), deserializedProblem.getSuggestion());
    }
}
