package com.aicodereview.model;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.List;

/**
 * 标准化评审报告实体类
 * 用于生成标准JSON格式的评审结果
 */
public class StandardReviewReport {
    
    @JsonProperty("total_problems")
    private final int totalProblems;
    
    @JsonProperty("problems")
    private final List<ReviewProblem> problems;

    @JsonCreator
    public StandardReviewReport(
            @JsonProperty("total_problems") int totalProblems, 
            @JsonProperty("problems") List<ReviewProblem> problems) {
        this.totalProblems = totalProblems;
        this.problems = problems;
    }

    public int getTotalProblems() {
        return totalProblems;
    }

    public List<ReviewProblem> getProblems() {
        return problems;
    }

    @Override
    public String toString() {
        return "StandardReviewReport{" +
                "totalProblems=" + totalProblems +
                ", problems=" + problems +
                '}';
    }
}
