package com.aicodereview.service;

import com.aicodereview.entity.CodeReviewRecord;
import com.aicodereview.repository.CodeReviewRecordMapper;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

import static org.junit.jupiter.api.Assertions.*;

/**
 * StatisticsService集成测试
 * 验证logtime字段的自动填充是否正常工作
 */
@SpringBootTest
@ActiveProfiles("test")
@Transactional
class StatisticsServiceIntegrationTest {

    @Autowired
    private StatisticsService statisticsService;

    @Autowired
    private CodeReviewRecordMapper mapper;

    @Test
    void testSaveStatisticsWithAutoFillLogtime() {
        // 测试基本的保存方法
        statisticsService.saveStatistics("Java", 10, 8);

        // 验证数据是否正确保存
        Long count = mapper.selectCount(null);
        assertTrue(count > 0, "应该至少有一条记录");

        // 查询刚插入的记录
        CodeReviewRecord record = mapper.selectList(null).get(0);
        assertNotNull(record.getLogtime(), "logtime字段不应该为null");
        assertEquals("Java", record.getProgrammingLanguage());
        assertEquals(10, record.getSuggestionCount());
        assertEquals(8, record.getAdoptedCount());
    }

    @Test
    void testSaveStatisticsWithSessionDuration() {
        // 测试包含session_duration的保存方法
        statisticsService.saveStatistics("Python", 5, 3, 300);

        // 验证数据是否正确保存
        Long count = mapper.selectCount(null);
        assertTrue(count > 0, "应该至少有一条记录");

        // 查询刚插入的记录
        CodeReviewRecord record = mapper.selectList(null).get(0);
        assertNotNull(record.getLogtime(), "logtime字段不应该为null");
        assertEquals("Python", record.getProgrammingLanguage());
        assertEquals(5, record.getSuggestionCount());
        assertEquals(3, record.getAdoptedCount());
        assertEquals(300, record.getSessionDuration());
    }

    @Test
    void testDirectMapperInsert() {
        // 直接测试mapper插入，验证自动填充是否工作
        CodeReviewRecord record = new CodeReviewRecord("JavaScript", 7, 5, 240);
        
        // 验证构造函数是否设置了logtime
        assertNotNull(record.getLogtime(), "构造函数应该设置logtime");

        // 插入记录
        int result = mapper.insert(record);
        assertEquals(1, result, "应该插入一条记录");
        assertNotNull(record.getId(), "ID应该被自动生成");

        // 查询验证
        CodeReviewRecord savedRecord = mapper.selectById(record.getId());
        assertNotNull(savedRecord);
        assertNotNull(savedRecord.getLogtime(), "保存后logtime不应该为null");
        assertEquals("JavaScript", savedRecord.getProgrammingLanguage());
        assertEquals(7, savedRecord.getSuggestionCount());
        assertEquals(5, savedRecord.getAdoptedCount());
        assertEquals(240, savedRecord.getSessionDuration());
    }

    @Test
    void testEntityDefaultConstructor() {
        // 测试实体类的默认构造函数是否设置了logtime
        CodeReviewRecord record = new CodeReviewRecord();
        assertNotNull(record.getLogtime(), "默认构造函数应该设置logtime");

        // 设置其他必需字段
        record.setProgrammingLanguage("TypeScript");
        record.setSuggestionCount(12);
        record.setAdoptedCount(9);
        record.setSessionDuration(360);

        // 插入记录
        int result = mapper.insert(record);
        assertEquals(1, result, "应该插入一条记录");

        // 验证保存的记录
        CodeReviewRecord savedRecord = mapper.selectById(record.getId());
        assertNotNull(savedRecord);
        assertNotNull(savedRecord.getLogtime());
        assertEquals("TypeScript", savedRecord.getProgrammingLanguage());
    }
}
