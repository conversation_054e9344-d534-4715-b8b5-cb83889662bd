-- 初始化数据库脚本

-- 创建代码评审记录表
CREATE TABLE IF NOT EXISTS code_review_record (
    id SERIAL PRIMARY KEY,
    logtime TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP NOT NULL,
    programming_language VARCHAR(50) NOT NULL,
    suggestion_count INTEGER NOT NULL DEFAULT 0,
    adopted_count INTEGER NOT NULL DEFAULT 0,
    session_duration INTEGER NULL,
    CONSTRAINT code_review_record_adopted_count_check CHECK ((adopted_count >= 0)),
    CONSTRAINT code_review_record_suggestion_count_check CHECK ((suggestion_count >= 0))
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_code_review_record_language ON code_review_record(programming_language);
CREATE INDEX IF NOT EXISTS idx_code_review_record_logtime ON code_review_record(logtime);

-- 插入示例数据（可选）
INSERT INTO code_review_record (programming_language, suggestion_count, adopted_count, session_duration) VALUES
('Java', 50, 30, 300),
('Python', 25, 20, 180),
('JavaScript', 40, 25, 240)
ON CONFLICT DO NOTHING;
