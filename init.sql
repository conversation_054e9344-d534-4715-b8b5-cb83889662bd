-- 初始化数据库脚本

-- 创建代码评审记录表
CREATE TABLE IF NOT EXISTS code_review_record (
    id BIGSERIAL PRIMARY KEY,
    programming_language VARCHAR(50) NOT NULL,
    suggestion_count INTEGER NOT NULL DEFAULT 0,
    adopted_count INTEGER NOT NULL DEFAULT 0,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_code_review_record_language ON code_review_record(programming_language);
CREATE INDEX IF NOT EXISTS idx_code_review_record_created_at ON code_review_record(created_at);

-- 插入示例数据（可选）
INSERT INTO code_review_record (programming_language, suggestion_count, adopted_count) VALUES
('Java', 50, 30),
('Python', 25, 20),
('JavaScript', 40, 25)
ON CONFLICT DO NOTHING;
