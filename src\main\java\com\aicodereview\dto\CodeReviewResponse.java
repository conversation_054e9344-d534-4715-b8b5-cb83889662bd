package com.aicodereview.dto;

import java.util.List;

/**
 * 代码评审响应DTO
 */
public class CodeReviewResponse {

    private String resultDir;
    private List<String> filesReceived;

    // Constructors
    public CodeReviewResponse() {}

    public CodeReviewResponse(String resultDir, List<String> filesReceived) {
        this.resultDir = resultDir;
        this.filesReceived = filesReceived;
    }

    // Getters and Setters
    public String getResultDir() {
        return resultDir;
    }

    public void setResultDir(String resultDir) {
        this.resultDir = resultDir;
    }

    public List<String> getFilesReceived() {
        return filesReceived;
    }

    public void setFilesReceived(List<String> filesReceived) {
        this.filesReceived = filesReceived;
    }

    @Override
    public String toString() {
        return "CodeReviewResponse{" +
                "resultDir='" + resultDir + '\'' +
                ", filesReceived=" + filesReceived +
                '}';
    }
}
