# AI Code Review Backend (Java 21)

基于Java 21和Spring Boot 3.x重构的AI代码评审后端服务，使用DeepSeek API进行代码评审。

## 技术栈

- **Java 21** - 使用最新的Java特性
- **Spring Boot 3.2+** - 现代化的Spring框架
- **MyBatis-Plus** - 数据访问层
- **PostgreSQL** - 数据库
- **DeepSeek API** - AI代码评审
- **Maven** - 构建工具
- **Docker** - 容器化部署

## 功能特性

- ✅ 多文件Java代码上传和评审
- ✅ 异步任务处理和状态跟踪
- ✅ 多维度代码评审（结构、最佳实践、Bug检测、性能、安全性、可维护性）
- ✅ 评审结果下载（ZIP格式）
- ✅ 统计信息管理
- ✅ RESTful API设计
- ✅ 全局异常处理
- ✅ 容器化部署支持

## 快速开始

### 环境要求

- Java 21+
- Maven 3.9+
- PostgreSQL 12+
- DeepSeek API Key

### 本地开发

1. **克隆项目**
```bash
git clone <repository-url>
cd java-backend
```

2. **配置环境变量**
```bash
export DEEPSEEK_API_KEY=your-deepseek-api-key
export SPRING_PROFILES_ACTIVE=dev
```

3. **启动数据库**
```bash
# 使用Docker启动PostgreSQL
docker run -d \
  --name postgres-dev \
  -e POSTGRES_DB=codeview_dev \
  -e POSTGRES_USER=postgres \
  -e POSTGRES_PASSWORD=password \
  -p 5432:5432 \
  postgres:15-alpine
```

4. **运行应用**
```bash
chmod +x start.sh
./start.sh
```

或者直接使用Maven：
```bash
mvn spring-boot:run -Dspring-boot.run.profiles=dev
```

### Docker部署

1. **使用Docker Compose**
```bash
# 设置环境变量
export DEEPSEEK_API_KEY=your-deepseek-api-key

# 启动所有服务
docker-compose up -d
```

2. **单独构建Docker镜像**
```bash
docker build -t ai-code-review-backend .
docker run -p 8080:8080 \
  -e DEEPSEEK_API_KEY=your-api-key \
  ai-code-review-backend
```

## API文档

### 代码评审

**POST** `/api/code_review`
- 上传Java文件进行代码评审
- 支持多文件上传
- 返回任务ID用于状态查询

**GET** `/api/code_review/status/{resultDir}`
- 查询评审任务状态
- 返回进度、当前处理文件等信息

**POST** `/api/code_review/download`
- 下载评审结果ZIP文件

### 统计管理

**POST** `/api/save/statistics`
- 保存评审统计信息

**GET** `/api/query/statistics`
- 获取按编程语言分组的统计信息

## 配置说明

### 应用配置

主要配置文件：
- `application.yml` - 基础配置
- `application-dev.yml` - 开发环境配置
- `application-prod.yml` - 生产环境配置

### 环境变量

| 变量名 | 描述 | 默认值 |
|--------|------|--------|
| `DEEPSEEK_API_KEY` | DeepSeek API密钥 | - |
| `SPRING_PROFILES_ACTIVE` | 激活的配置文件 | `dev` |
| `JAVA_OPTS` | JVM参数 | `-Xmx2g -Xms1g` |

### 数据库配置

开发环境：
```yaml
spring:
  datasource:
    url: *********************************************
    username: postgres
    password: password
```

生产环境：
```yaml
spring:
  datasource:
    url: jdbc:postgresql://************:9845/CodeView
    username: postgres
    password: Ceiec4567%%
```

## 项目结构

```
java-backend/
├── src/main/java/com/aicodereview/
│   ├── config/          # 配置类
│   ├── controller/      # REST控制器
│   ├── dto/            # 数据传输对象
│   ├── entity/         # JPA实体
│   ├── exception/      # 异常处理
│   ├── model/          # 业务模型
│   ├── repository/     # 数据访问层
│   └── service/        # 业务服务层
├── src/main/resources/
│   ├── prompts/        # 评审提示词模板
│   └── application*.yml # 配置文件
├── docker-compose.yml  # Docker编排
├── Dockerfile         # Docker镜像构建
└── start.sh          # 启动脚本
```

## 开发指南

### 添加新的评审规则

1. 在 `src/main/resources/prompts/` 目录下添加新的 `.txt` 文件
2. 文件名将作为规则名称
3. 文件内容为评审提示词

### 扩展API接口

1. 在相应的Controller中添加新的端点
2. 创建对应的DTO类
3. 在Service层实现业务逻辑

## 监控和日志

### 健康检查

- **URL**: `/api/actuator/health`
- **方法**: GET
- **描述**: 应用健康状态检查

### 日志配置

- 开发环境：控制台输出，DEBUG级别
- 生产环境：文件输出，INFO级别
- 日志文件：`/app/logs/code-review-backend.log`

## 故障排除

### 常见问题

1. **Java版本不兼容**
   - 确保使用Java 21或更高版本

2. **数据库连接失败**
   - 检查数据库服务是否启动
   - 验证连接配置是否正确

3. **DeepSeek API调用失败**
   - 检查API密钥是否正确
   - 确认网络连接正常

4. **文件上传失败**
   - 检查文件大小是否超过限制（默认50MB）
   - 确认上传目录权限

### 日志查看

```bash
# 查看应用日志
tail -f logs/code-review-backend.log

# Docker环境查看日志
docker-compose logs -f code-review-backend
```

## 贡献指南

1. Fork项目
2. 创建特性分支
3. 提交更改
4. 推送到分支
5. 创建Pull Request

## 许可证

MIT License
