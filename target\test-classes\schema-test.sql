-- 测试环境数据库表结构

-- 创建代码评审记录表
CREATE TABLE IF NOT EXISTS code_review_record (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    programming_language VARCHAR(50) NOT NULL,
    suggestion_count INTEGER NOT NULL DEFAULT 0,
    adopted_count INTEGER NOT NULL DEFAULT 0,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_code_review_record_language ON code_review_record(programming_language);
CREATE INDEX IF NOT EXISTS idx_code_review_record_created_at ON code_review_record(created_at);
