<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="RemoteRepositoriesConfiguration">
    <remote-repository>
      <option name="id" value="10.12.135.233_9085" />
      <option name="name" value="10.12.135.233_9085" />
      <option name="url" value="http://10.12.135.233:9085/repository/maven-public" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="10.12.135.233_9086" />
      <option name="name" value="10.12.135.233_9086" />
      <option name="url" value="http://10.12.135.233:9086/repository/maven-public" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="10.12.135.233_9084" />
      <option name="name" value="10.12.135.233_9084" />
      <option name="url" value="http://10.12.135.233:9084/repository/maven-public" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="10.12.135.233_9090" />
      <option name="name" value="10.12.135.233_9090" />
      <option name="url" value="http://10.12.135.233:9090/repository/maven-public" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="central" />
      <option name="name" value="Maven Central repository" />
      <option name="url" value="https://repo1.maven.org/maven2" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="10.12.135.233_9089" />
      <option name="name" value="10.12.135.233_9089" />
      <option name="url" value="http://10.12.135.233:9089/repository/maven-public" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="10.12.135.233_9081" />
      <option name="name" value="10.12.135.233_9081" />
      <option name="url" value="http://10.12.135.233:9081/repository/maven-public" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="10.12.135.233_9091" />
      <option name="name" value="10.12.135.233_9091" />
      <option name="url" value="http://10.12.135.233:9091/repository/maven-public" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="10.12.135.233_9082" />
      <option name="name" value="10.12.135.233_9082" />
      <option name="url" value="http://10.12.135.233:9082/repository/maven-public" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="10.12.135.233_9088" />
      <option name="name" value="10.12.135.233_9088" />
      <option name="url" value="http://10.12.135.233:9088/repository/maven-public" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="nexus-aliyun" />
      <option name="name" value="nexus-aliyun" />
      <option name="url" value="http://maven.aliyun.com/nexus/content/groups/public" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="10.12.135.233_9092" />
      <option name="name" value="10.12.135.233_9092" />
      <option name="url" value="http://10.12.135.233:9092/repository/maven-public" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="central" />
      <option name="name" value="Central Repository" />
      <option name="url" value="http://maven.aliyun.com/nexus/content/groups/public" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="jboss.community" />
      <option name="name" value="JBoss Community repository" />
      <option name="url" value="https://repository.jboss.org/nexus/content/repositories/public/" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="10.12.135.233_9087" />
      <option name="name" value="10.12.135.233_9087" />
      <option name="url" value="http://10.12.135.233:9087/repository/maven-public" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="10.12.135.233_9083" />
      <option name="name" value="10.12.135.233_9083" />
      <option name="url" value="http://10.12.135.233:9083/repository/maven-public" />
    </remote-repository>
  </component>
</project>