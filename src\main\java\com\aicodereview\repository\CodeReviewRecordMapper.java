package com.aicodereview.repository;

import com.aicodereview.entity.CodeReviewRecord;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * Code Review Record Mapper
 */
@Mapper
public interface CodeReviewRecordMapper extends BaseMapper<CodeReviewRecord> {

    /**
     * 分页查询记录，按创建时间倒序
     */
    @Select("SELECT * FROM code_review_record ORDER BY logtime DESC")
    IPage<CodeReviewRecord> selectAllByOrderByLogtimeDesc(Page<CodeReviewRecord> page);

    /**
     * 按编程语言分组统计
     */
    @Select("""
        SELECT programming_language as programmingLanguage,
               SUM(suggestion_count) as totalSuggestionCount,
               SUM(adopted_count) as totalAdoptedCount
        FROM code_review_record
        GROUP BY programming_language
        ORDER BY programming_language
        """)
    List<StatisticsProjection> getStatisticsByProgrammingLanguage();

    /**
     * 统计投影接口
     */
    interface StatisticsProjection {
        String getProgrammingLanguage();
        Long getTotalSuggestionCount();
        Long getTotalAdoptedCount();
    }
}
