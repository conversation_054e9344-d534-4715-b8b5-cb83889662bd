package com.aicodereview.dto;

/**
 * 统计信息响应DTO
 */
public class StatisticsResponse {

    private String programmingLanguage;
    private Long totalSuggestionCount;
    private Long totalAdoptedCount;
    private String adoptionRate;

    // Constructors
    public StatisticsResponse() {}

    public StatisticsResponse(String programmingLanguage, Long totalSuggestionCount, Long totalAdoptedCount) {
        this.programmingLanguage = programmingLanguage;
        this.totalSuggestionCount = totalSuggestionCount;
        this.totalAdoptedCount = totalAdoptedCount;
        this.adoptionRate = calculateAdoptionRate(totalSuggestionCount, totalAdoptedCount);
    }

    private String calculateAdoptionRate(Long totalSuggestions, Long totalAdopted) {
        if (totalSuggestions == null || totalSuggestions == 0) {
            return "0%";
        }
        double rate = (totalAdopted.doubleValue() / totalSuggestions.doubleValue()) * 100;
        return String.format("%.1f%%", rate);
    }

    // Getters and Setters
    public String getProgrammingLanguage() {
        return programmingLanguage;
    }

    public void setProgrammingLanguage(String programmingLanguage) {
        this.programmingLanguage = programmingLanguage;
    }

    public Long getTotalSuggestionCount() {
        return totalSuggestionCount;
    }

    public void setTotalSuggestionCount(Long totalSuggestionCount) {
        this.totalSuggestionCount = totalSuggestionCount;
        this.adoptionRate = calculateAdoptionRate(totalSuggestionCount, this.totalAdoptedCount);
    }

    public Long getTotalAdoptedCount() {
        return totalAdoptedCount;
    }

    public void setTotalAdoptedCount(Long totalAdoptedCount) {
        this.totalAdoptedCount = totalAdoptedCount;
        this.adoptionRate = calculateAdoptionRate(this.totalSuggestionCount, totalAdoptedCount);
    }

    public String getAdoptionRate() {
        return adoptionRate;
    }

    public void setAdoptionRate(String adoptionRate) {
        this.adoptionRate = adoptionRate;
    }

    @Override
    public String toString() {
        return "StatisticsResponse{" +
                "programmingLanguage='" + programmingLanguage + '\'' +
                ", totalSuggestionCount=" + totalSuggestionCount +
                ", totalAdoptedCount=" + totalAdoptedCount +
                ", adoptionRate='" + adoptionRate + '\'' +
                '}';
    }
}
