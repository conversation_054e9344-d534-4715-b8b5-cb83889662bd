package com.aicodereview.dto;

import java.time.LocalDateTime;

/**
 * 任务状态响应DTO
 */
public class TaskStatusResponse {

    private String status;
    private Double progress;
    private String currentFile;
    private Integer processedFiles;
    private Integer totalFiles;
    private LocalDateTime startTime;
    private LocalDateTime endTime;
    private String error;

    // Constructors
    public TaskStatusResponse() {}

    // Getters and Setters
    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public Double getProgress() {
        return progress;
    }

    public void setProgress(Double progress) {
        this.progress = progress;
    }

    public String getCurrentFile() {
        return currentFile;
    }

    public void setCurrentFile(String currentFile) {
        this.currentFile = currentFile;
    }

    public Integer getProcessedFiles() {
        return processedFiles;
    }

    public void setProcessedFiles(Integer processedFiles) {
        this.processedFiles = processedFiles;
    }

    public Integer getTotalFiles() {
        return totalFiles;
    }

    public void setTotalFiles(Integer totalFiles) {
        this.totalFiles = totalFiles;
    }

    public LocalDateTime getStartTime() {
        return startTime;
    }

    public void setStartTime(LocalDateTime startTime) {
        this.startTime = startTime;
    }

    public LocalDateTime getEndTime() {
        return endTime;
    }

    public void setEndTime(LocalDateTime endTime) {
        this.endTime = endTime;
    }

    public String getError() {
        return error;
    }

    public void setError(String error) {
        this.error = error;
    }

    @Override
    public String toString() {
        return "TaskStatusResponse{" +
                "status='" + status + '\'' +
                ", progress=" + progress +
                ", currentFile='" + currentFile + '\'' +
                ", processedFiles=" + processedFiles +
                ", totalFiles=" + totalFiles +
                ", startTime=" + startTime +
                ", endTime=" + endTime +
                ", error='" + error + '\'' +
                '}';
    }
}
