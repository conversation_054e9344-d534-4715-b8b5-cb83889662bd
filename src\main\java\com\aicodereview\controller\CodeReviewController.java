package com.aicodereview.controller;

import com.aicodereview.dto.ApiResponse;
import com.aicodereview.dto.CodeReviewResponse;
import com.aicodereview.dto.TaskStatusResponse;
import com.aicodereview.model.StandardReviewReport;
import com.aicodereview.model.TaskStatus;
import com.aicodereview.service.CodeReviewService;
import com.aicodereview.service.FileService;
import com.aicodereview.service.TaskStatusService;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.FileSystemResource;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.nio.file.Path;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 代码评审控制器
 */
@RestController
@RequestMapping("/code_review")
public class CodeReviewController {

    private static final Logger logger = LoggerFactory.getLogger(CodeReviewController.class);

    private final CodeReviewService codeReviewService;
    private final FileService fileService;
    private final TaskStatusService taskStatusService;
    private final ObjectMapper objectMapper;

    @Autowired
    public CodeReviewController(CodeReviewService codeReviewService,
                              FileService fileService,
                              TaskStatusService taskStatusService,
                              ObjectMapper objectMapper) {
        this.codeReviewService = codeReviewService;
        this.fileService = fileService;
        this.taskStatusService = taskStatusService;
        this.objectMapper = objectMapper;
    }

    /**
     * 代码评审接口
     */
    @PostMapping(consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public ResponseEntity<ApiResponse<CodeReviewResponse>> codeReview(
            @RequestParam("files") List<MultipartFile> files,
            @RequestParam(value = "question", required = false) String customQuestion) {
        
        try {
            logger.info("收到代码评审请求，文件数: {}", files.size());

            // 验证文件
            if (files == null || files.isEmpty()) {
                return ResponseEntity.badRequest()
                    .body(ApiResponse.error("至少需要上传一个Java文件"));
            }

            // 创建结果目录
            String resultDir = fileService.createResultDirectory();

            // 保存上传的文件
            List<CodeReviewService.FileInfo> fileInfos = fileService.saveUploadedFiles(files, resultDir);

            // 启动异步评审任务
            codeReviewService.reviewCodeAsync(fileInfos, resultDir, customQuestion);

            // 返回响应
            CodeReviewResponse response = new CodeReviewResponse(
                resultDir,
                fileInfos.stream().map(CodeReviewService.FileInfo::getFilename).collect(Collectors.toList())
            );

            return ResponseEntity.ok(ApiResponse.success("代码评审任务已开始，请稍后查看结果", response));

        } catch (IllegalArgumentException e) {
            logger.warn("请求参数错误: {}", e.getMessage());
            return ResponseEntity.badRequest().body(ApiResponse.error(e.getMessage()));
        } catch (Exception e) {
            logger.error("代码评审请求处理失败", e);
            return ResponseEntity.internalServerError()
                .body(ApiResponse.error("服务器内部错误: " + e.getMessage()));
        }
    }

    /**
     * 获取代码评审任务状态
     */
    @GetMapping("/status/{resultDir}")
    public ResponseEntity<ApiResponse<TaskStatusResponse>> getReviewStatus(@PathVariable String resultDir) {
        try {
            TaskStatus status = taskStatusService.getStatus(resultDir);
            if (status == null) {
                return ResponseEntity.notFound().build();
            }

            TaskStatusResponse response = new TaskStatusResponse();
            response.setStatus(status.getStatus().getValue());
            response.setProgress(status.getProgress());
            response.setCurrentFile(status.getCurrentFile());
            response.setProcessedFiles(status.getProcessedFiles());
            response.setTotalFiles(status.getTotalFiles());
            response.setStartTime(status.getStartTime());
            response.setEndTime(status.getEndTime());
            response.setError(status.getError());

            return ResponseEntity.ok(ApiResponse.success(response));

        } catch (Exception e) {
            logger.error("获取任务状态失败: {}", resultDir, e);
            return ResponseEntity.internalServerError()
                .body(ApiResponse.error("获取任务状态失败: " + e.getMessage()));
        }
    }

    /**
     * 下载评审结果ZIP文件
     */
    @PostMapping("/download")
    public ResponseEntity<?> downloadResults(@RequestParam("path") String resultDir) {
        try {
            logger.info("下载评审结果: {}", resultDir);

            Path zipFile = fileService.createZipFile(resultDir);
            Resource resource = new FileSystemResource(zipFile);

            if (!resource.exists()) {
                return ResponseEntity.notFound().build();
            }

            // 设置响应头
            HttpHeaders headers = new HttpHeaders();
            headers.add(HttpHeaders.CONTENT_DISPOSITION, 
                "attachment; filename=\"" + resultDir + ".zip\"");
            headers.add(HttpHeaders.CONTENT_TYPE, "application/zip");

            return ResponseEntity.ok()
                .headers(headers)
                .contentLength(resource.contentLength())
                .body(resource);

        } catch (IOException e) {
            logger.error("创建ZIP文件失败: {}", resultDir, e);
            return ResponseEntity.internalServerError()
                .body(ApiResponse.error("创建压缩包失败: " + e.getMessage()));
        } catch (Exception e) {
            logger.error("下载请求处理失败: {}", resultDir, e);
            return ResponseEntity.internalServerError()
                .body(ApiResponse.error("下载失败: " + e.getMessage()));
        }
    }

    /**
     * 获取目录文件列表
     */
    @GetMapping("/list-files/")
    public ResponseEntity<ApiResponse<List<String>>> listFiles(@RequestParam("directory") String directory) {
        try {
            List<String> files = fileService.listFiles(directory);
            return ResponseEntity.ok(ApiResponse.success(files));

        } catch (IllegalArgumentException e) {
            logger.warn("目录不存在: {}", directory);
            return ResponseEntity.notFound().build();
        } catch (Exception e) {
            logger.error("获取文件列表失败: {}", directory, e);
            return ResponseEntity.internalServerError()
                .body(ApiResponse.error("获取文件列表失败: " + e.getMessage()));
        }
    }

    /**
     * 获取评审结果JSON
     */
    @GetMapping("/review/")
    public ResponseEntity<ApiResponse<StandardReviewReport>> getReview(@RequestParam("dir_path") String dirPath) {
        try {
            String reviewContent = fileService.readFileContent(dirPath, "review.json");

            // 将JSON字符串反序列化为StandardReviewReport对象
            StandardReviewReport reviewReport = objectMapper.readValue(reviewContent, StandardReviewReport.class);

            // 返回对象而不是字符串，这样就不会有转义字符了
            return ResponseEntity.ok(ApiResponse.success(reviewReport));

        } catch (IllegalArgumentException e) {
            logger.warn("文件不存在: {}/review.json", dirPath);
            return ResponseEntity.notFound().build();
        } catch (Exception e) {
            logger.error("读取评审结果失败: {}", dirPath, e);
            return ResponseEntity.internalServerError()
                .body(ApiResponse.error("读取评审结果失败: " + e.getMessage()));
        }
    }
}
