package com.aicodereview.entity;

import com.baomidou.mybatisplus.annotation.*;

import java.time.OffsetDateTime;

/**
 * Code Review Record Entity
 * 对应数据库表 code_review_record
 */
@TableName("code_review_record")
public class CodeReviewRecord {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @TableField("programming_language")
    private String programmingLanguage;

    @TableField("suggestion_count")
    private Integer suggestionCount;

    @TableField("adopted_count")
    private Integer adoptedCount;

    @TableField("session_duration")
    private Integer sessionDuration;

    @TableField(value = "logtime", fill = FieldFill.INSERT)
    private OffsetDateTime logtime;

    // Constructors
    public CodeReviewRecord() {}

    public CodeReviewRecord(String programmingLanguage, Integer suggestionCount, Integer adoptedCount) {
        this.programmingLanguage = programmingLanguage;
        this.suggestionCount = suggestionCount;
        this.adoptedCount = adoptedCount;
    }

    public CodeReviewRecord(String programmingLanguage, Integer suggestionCount, Integer adoptedCount, Integer sessionDuration) {
        this.programmingLanguage = programmingLanguage;
        this.suggestionCount = suggestionCount;
        this.adoptedCount = adoptedCount;
        this.sessionDuration = sessionDuration;
    }

    // Getters and Setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getProgrammingLanguage() {
        return programmingLanguage;
    }

    public void setProgrammingLanguage(String programmingLanguage) {
        this.programmingLanguage = programmingLanguage;
    }

    public Integer getSuggestionCount() {
        return suggestionCount;
    }

    public void setSuggestionCount(Integer suggestionCount) {
        this.suggestionCount = suggestionCount;
    }

    public Integer getAdoptedCount() {
        return adoptedCount;
    }

    public void setAdoptedCount(Integer adoptedCount) {
        this.adoptedCount = adoptedCount;
    }

    public Integer getSessionDuration() {
        return sessionDuration;
    }

    public void setSessionDuration(Integer sessionDuration) {
        this.sessionDuration = sessionDuration;
    }

    public OffsetDateTime getLogtime() {
        return logtime;
    }

    public void setLogtime(OffsetDateTime logtime) {
        this.logtime = logtime;
    }

    @Override
    public String toString() {
        return "CodeReviewRecord{" +
                "id=" + id +
                ", programmingLanguage='" + programmingLanguage + '\'' +
                ", suggestionCount=" + suggestionCount +
                ", adoptedCount=" + adoptedCount +
                ", sessionDuration=" + sessionDuration +
                ", logtime=" + logtime +
                '}';
    }
}
