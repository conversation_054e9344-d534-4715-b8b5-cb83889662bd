import com.aicodereview.entity.CodeReviewRecord;
import java.time.OffsetDateTime;

/**
 * 简单的测试类，验证logtime字段的设置
 */
public class TestLogtimeField {
    
    public static void main(String[] args) {
        System.out.println("=== 测试CodeReviewRecord的logtime字段 ===");
        
        // 测试1: 默认构造函数
        System.out.println("\n1. 测试默认构造函数:");
        CodeReviewRecord record1 = new CodeReviewRecord();
        System.out.println("logtime是否为null: " + (record1.getLogtime() == null));
        System.out.println("logtime值: " + record1.getLogtime());
        
        // 测试2: 带参数的构造函数
        System.out.println("\n2. 测试带参数的构造函数:");
        CodeReviewRecord record2 = new CodeReviewRecord("Java", 10, 8);
        System.out.println("logtime是否为null: " + (record2.getLogtime() == null));
        System.out.println("logtime值: " + record2.getLogtime());
        System.out.println("编程语言: " + record2.getProgrammingLanguage());
        System.out.println("建议数: " + record2.getSuggestionCount());
        System.out.println("采纳数: " + record2.getAdoptedCount());
        
        // 测试3: 带session_duration的构造函数
        System.out.println("\n3. 测试带session_duration的构造函数:");
        CodeReviewRecord record3 = new CodeReviewRecord("Python", 5, 3, 300);
        System.out.println("logtime是否为null: " + (record3.getLogtime() == null));
        System.out.println("logtime值: " + record3.getLogtime());
        System.out.println("编程语言: " + record3.getProgrammingLanguage());
        System.out.println("建议数: " + record3.getSuggestionCount());
        System.out.println("采纳数: " + record3.getAdoptedCount());
        System.out.println("会话时长: " + record3.getSessionDuration());
        
        // 测试4: 手动设置logtime
        System.out.println("\n4. 测试手动设置logtime:");
        CodeReviewRecord record4 = new CodeReviewRecord();
        OffsetDateTime customTime = OffsetDateTime.now().minusHours(1);
        record4.setLogtime(customTime);
        record4.setProgrammingLanguage("JavaScript");
        record4.setSuggestionCount(7);
        record4.setAdoptedCount(5);
        record4.setSessionDuration(240);
        
        System.out.println("logtime是否为null: " + (record4.getLogtime() == null));
        System.out.println("logtime值: " + record4.getLogtime());
        System.out.println("是否是自定义时间: " + record4.getLogtime().equals(customTime));
        
        // 测试5: toString方法
        System.out.println("\n5. 测试toString方法:");
        System.out.println(record3.toString());
        
        System.out.println("\n=== 测试完成 ===");
        System.out.println("所有记录的logtime字段都应该不为null，这样就不会出现数据库约束违反错误。");
    }
}
