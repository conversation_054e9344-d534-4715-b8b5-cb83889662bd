package com.aicodereview.model;

import java.time.LocalDateTime;

/**
 * 任务状态模型
 */
public class TaskStatus {

    public enum Status {
        PROCESSING("processing"),
        COMPLETED("completed"),
        FAILED("failed");

        private final String value;

        Status(String value) {
            this.value = value;
        }

        public String getValue() {
            return value;
        }

        @Override
        public String toString() {
            return value;
        }
    }

    private String resultDir;
    private Status status;
    private Double progress;
    private String currentFile;
    private Integer processedFiles;
    private Integer totalFiles;
    private LocalDateTime startTime;
    private LocalDateTime endTime;
    private String error;

    // Constructors
    public TaskStatus() {}

    public TaskStatus(String resultDir, Integer totalFiles) {
        this.resultDir = resultDir;
        this.status = Status.PROCESSING;
        this.progress = 0.0;
        this.processedFiles = 0;
        this.totalFiles = totalFiles;
        this.startTime = LocalDateTime.now();
    }

    // Getters and Setters
    public String getResultDir() {
        return resultDir;
    }

    public void setResultDir(String resultDir) {
        this.resultDir = resultDir;
    }

    public Status getStatus() {
        return status;
    }

    public void setStatus(Status status) {
        this.status = status;
    }

    public Double getProgress() {
        return progress;
    }

    public void setProgress(Double progress) {
        this.progress = progress;
    }

    public String getCurrentFile() {
        return currentFile;
    }

    public void setCurrentFile(String currentFile) {
        this.currentFile = currentFile;
    }

    public Integer getProcessedFiles() {
        return processedFiles;
    }

    public void setProcessedFiles(Integer processedFiles) {
        this.processedFiles = processedFiles;
    }

    public Integer getTotalFiles() {
        return totalFiles;
    }

    public void setTotalFiles(Integer totalFiles) {
        this.totalFiles = totalFiles;
    }

    public LocalDateTime getStartTime() {
        return startTime;
    }

    public void setStartTime(LocalDateTime startTime) {
        this.startTime = startTime;
    }

    public LocalDateTime getEndTime() {
        return endTime;
    }

    public void setEndTime(LocalDateTime endTime) {
        this.endTime = endTime;
    }

    public String getError() {
        return error;
    }

    public void setError(String error) {
        this.error = error;
    }

    /**
     * 增加处理进度
     */
    public void incrementProgress() {
        this.processedFiles++;
        if (this.totalFiles > 0) {
            this.progress = Math.min(100.0, 
                Math.round((this.processedFiles.doubleValue() / this.totalFiles.doubleValue()) * 100 * 10.0) / 10.0);
        }
    }

    /**
     * 标记任务完成
     */
    public void markCompleted() {
        this.status = Status.COMPLETED;
        this.endTime = LocalDateTime.now();
        this.progress = 100.0;
    }

    /**
     * 标记任务失败
     */
    public void markFailed(String error) {
        this.status = Status.FAILED;
        this.endTime = LocalDateTime.now();
        this.error = error;
    }

    @Override
    public String toString() {
        return "TaskStatus{" +
                "resultDir='" + resultDir + '\'' +
                ", status=" + status +
                ", progress=" + progress +
                ", currentFile='" + currentFile + '\'' +
                ", processedFiles=" + processedFiles +
                ", totalFiles=" + totalFiles +
                ", startTime=" + startTime +
                ", endTime=" + endTime +
                ", error='" + error + '\'' +
                '}';
    }
}
