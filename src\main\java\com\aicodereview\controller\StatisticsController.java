package com.aicodereview.controller;

import com.aicodereview.dto.ApiResponse;
import com.aicodereview.dto.StatisticsResponse;
import com.aicodereview.entity.CodeReviewRecord;
import com.aicodereview.service.StatisticsService;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 统计控制器
 */
@RestController
@RequestMapping
public class StatisticsController {

    private static final Logger logger = LoggerFactory.getLogger(StatisticsController.class);

    private final StatisticsService statisticsService;

    @Autowired
    public StatisticsController(StatisticsService statisticsService) {
        this.statisticsService = statisticsService;
    }

    /**
     * 保存统计信息
     */
    @PostMapping("/save/statistics")
    public ResponseEntity<ApiResponse<String>> saveStatistics(@RequestBody StatisticsRequest request) {
        try {
            logger.info("保存统计信息: 语言={}, 建议数={}, 采纳数={}", 
                request.getProgrammingLanguage(), request.getSuggestionCount(), request.getAdoptedCount());

            statisticsService.saveStatistics(
                request.getProgrammingLanguage(),
                request.getSuggestionCount(),
                request.getAdoptedCount()
            );

            return ResponseEntity.ok(ApiResponse.success("保存成功"));

        } catch (Exception e) {
            logger.error("保存统计信息失败", e);
            return ResponseEntity.internalServerError()
                .body(ApiResponse.error("保存失败: " + e.getMessage()));
        }
    }

    /**
     * 获取按编程语言分组的统计信息
     */
    @GetMapping("/query/statistics")
    public ResponseEntity<ApiResponse<List<StatisticsResponse>>> getStatistics() {
        try {
            List<StatisticsResponse> statistics = statisticsService.getStatisticsByProgrammingLanguage();
            return ResponseEntity.ok(ApiResponse.success("成功", statistics));

        } catch (Exception e) {
            logger.error("获取统计信息失败", e);
            return ResponseEntity.internalServerError()
                .body(ApiResponse.error("获取统计信息失败: " + e.getMessage()));
        }
    }

    /**
     * 分页获取所有记录
     */
    @GetMapping("/records")
    public ResponseEntity<ApiResponse<IPage<CodeReviewRecord>>> getRecords(
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "10") int size) {
        try {
            IPage<CodeReviewRecord> records = statisticsService.getAllRecords(page, size);
            return ResponseEntity.ok(ApiResponse.success(records));

        } catch (Exception e) {
            logger.error("获取记录失败", e);
            return ResponseEntity.internalServerError()
                .body(ApiResponse.error("获取记录失败: " + e.getMessage()));
        }
    }

    /**
     * 获取所有记录
     */
    @GetMapping("/all-records")
    public ResponseEntity<ApiResponse<List<CodeReviewRecord>>> getAllRecords() {
        try {
            List<CodeReviewRecord> records = statisticsService.getAllRecords();
            return ResponseEntity.ok(ApiResponse.success(records));

        } catch (Exception e) {
            logger.error("获取所有记录失败", e);
            return ResponseEntity.internalServerError()
                .body(ApiResponse.error("获取所有记录失败: " + e.getMessage()));
        }
    }

    /**
     * 统计请求DTO
     */
    public static class StatisticsRequest {
        private String programmingLanguage;
        private Integer suggestionCount;
        private Integer adoptedCount;

        // Constructors
        public StatisticsRequest() {}

        public StatisticsRequest(String programmingLanguage, Integer suggestionCount, Integer adoptedCount) {
            this.programmingLanguage = programmingLanguage;
            this.suggestionCount = suggestionCount;
            this.adoptedCount = adoptedCount;
        }

        // Getters and Setters
        public String getProgrammingLanguage() {
            return programmingLanguage;
        }

        public void setProgrammingLanguage(String programmingLanguage) {
            this.programmingLanguage = programmingLanguage;
        }

        public Integer getSuggestionCount() {
            return suggestionCount;
        }

        public void setSuggestionCount(Integer suggestionCount) {
            this.suggestionCount = suggestionCount;
        }

        public Integer getAdoptedCount() {
            return adoptedCount;
        }

        public void setAdoptedCount(Integer adoptedCount) {
            this.adoptedCount = adoptedCount;
        }

        @Override
        public String toString() {
            return "StatisticsRequest{" +
                    "programmingLanguage='" + programmingLanguage + '\'' +
                    ", suggestionCount=" + suggestionCount +
                    ", adoptedCount=" + adoptedCount +
                    '}';
        }
    }
}
