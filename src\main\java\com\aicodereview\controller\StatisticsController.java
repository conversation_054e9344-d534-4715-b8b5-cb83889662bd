package com.aicodereview.controller;

import com.aicodereview.dto.ApiResponse;
import com.aicodereview.dto.StatisticsRequest;
import com.aicodereview.dto.StatisticsResponse;
import com.aicodereview.entity.CodeReviewRecord;
import com.aicodereview.service.StatisticsService;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 统计控制器
 */
@RestController
@RequestMapping
public class StatisticsController {

    private static final Logger logger = LoggerFactory.getLogger(StatisticsController.class);

    private final StatisticsService statisticsService;

    @Autowired
    public StatisticsController(StatisticsService statisticsService) {
        this.statisticsService = statisticsService;
    }

    /**
     * 保存统计信息
     */
    @PostMapping("/save/statistics")
    public ResponseEntity<ApiResponse<String>> saveStatistics(@RequestBody StatisticsRequest request) {
        try {
            logger.info("接收到请求对象: {}", request);
            logger.info("请求对象是否为null: {}", request == null);

            if (request != null) {
                logger.info("详细信息 - 语言: [{}], 建议数: [{}], 采纳数: [{}], 会话时长: [{}]",
                    request.getProgrammingLanguage(), request.getSuggestionCount(),
                    request.getAdoptedCount(), request.getSessionDuration());
            }

            // 根据是否有sessionDuration选择不同的方法
            if (request.getSessionDuration() != null) {
                statisticsService.saveStatistics(
                    request.getProgrammingLanguage(),
                    request.getSuggestionCount(),
                    request.getAdoptedCount(),
                    request.getSessionDuration()
                );
            } else {
                statisticsService.saveStatistics(
                    request.getProgrammingLanguage(),
                    request.getSuggestionCount(),
                    request.getAdoptedCount()
                );
            }

            return ResponseEntity.ok(ApiResponse.success("保存成功"));

        } catch (Exception e) {
            logger.error("保存统计信息失败", e);
            return ResponseEntity.internalServerError()
                .body(ApiResponse.error("保存失败: " + e.getMessage()));
        }
    }

    /**
     * 获取按编程语言分组的统计信息
     */
    @GetMapping("/query/statistics")
    public ResponseEntity<ApiResponse<List<StatisticsResponse>>> getStatistics() {
        try {
            List<StatisticsResponse> statistics = statisticsService.getStatisticsByProgrammingLanguage();
            return ResponseEntity.ok(ApiResponse.success("成功", statistics));

        } catch (Exception e) {
            logger.error("获取统计信息失败", e);
            return ResponseEntity.internalServerError()
                .body(ApiResponse.error("获取统计信息失败: " + e.getMessage()));
        }
    }

    /**
     * 分页获取所有记录
     */
    @GetMapping("/records")
    public ResponseEntity<ApiResponse<IPage<CodeReviewRecord>>> getRecords(
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "10") int size) {
        try {
            IPage<CodeReviewRecord> records = statisticsService.getAllRecords(page, size);
            return ResponseEntity.ok(ApiResponse.success(records));

        } catch (Exception e) {
            logger.error("获取记录失败", e);
            return ResponseEntity.internalServerError()
                .body(ApiResponse.error("获取记录失败: " + e.getMessage()));
        }
    }

    /**
     * 获取所有记录
     */
    @GetMapping("/all-records")
    public ResponseEntity<ApiResponse<List<CodeReviewRecord>>> getAllRecords() {
        try {
            List<CodeReviewRecord> records = statisticsService.getAllRecords();
            return ResponseEntity.ok(ApiResponse.success(records));

        } catch (Exception e) {
            logger.error("获取所有记录失败", e);
            return ResponseEntity.internalServerError()
                .body(ApiResponse.error("获取所有记录失败: " + e.getMessage()));
        }
    }
}
