#!/bin/bash

# AI Code Review Backend 启动脚本

set -e

echo "=== AI Code Review Backend 启动脚本 ==="

# 检查Java版本
echo "检查Java版本..."
if ! command -v java &> /dev/null; then
    echo "错误: 未找到Java，请安装Java 21"
    exit 1
fi

JAVA_VERSION=$(java -version 2>&1 | head -n 1 | cut -d'"' -f2 | cut -d'.' -f1)
if [ "$JAVA_VERSION" -lt 21 ]; then
    echo "错误: 需要Java 21或更高版本，当前版本: $JAVA_VERSION"
    exit 1
fi

echo "Java版本检查通过: $JAVA_VERSION"

# 检查Maven
echo "检查Maven..."
if ! command -v mvn &> /dev/null; then
    echo "错误: 未找到Maven，请安装Maven"
    exit 1
fi

# 设置环境变量
export SPRING_PROFILES_ACTIVE=${SPRING_PROFILES_ACTIVE:-dev}
export DEEPSEEK_API_KEY=${DEEPSEEK_API_KEY:-your-deepseek-api-key}

echo "当前配置文件: $SPRING_PROFILES_ACTIVE"

# 创建必要的目录
echo "创建必要的目录..."
mkdir -p results temp logs

# 构建项目
echo "构建项目..."
mvn clean package -DskipTests

# 检查构建结果
if [ ! -f "target/code-review-backend-1.0.0.jar" ]; then
    echo "错误: 构建失败，未找到JAR文件"
    exit 1
fi

echo "构建成功!"

# 启动应用
echo "启动应用..."
java -Xmx2g -Xms1g -XX:+UseG1GC \
     -Dspring.profiles.active=$SPRING_PROFILES_ACTIVE \
     -jar target/code-review-backend-1.0.0.jar

echo "应用已启动!"
