<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="1d562355-c2e1-4e82-87de-4554c003342a" name="Changes" comment="" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="MavenImportPreferences">
    <option name="generalSettings">
      <MavenGeneralSettings>
        <option name="customMavenHome" value="D:\tools\maven3" />
        <option name="localRepository" value="D:\Maven\mvnRepository-test" />
        <option name="mavenHomeTypeForPersistence" value="CUSTOM" />
        <option name="userSettingsFile" value="D:\tools\maven3\conf\settings.xml" />
      </MavenGeneralSettings>
    </option>
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 0
}</component>
  <component name="ProjectId" id="30rLo9Hu5dSayevO59kbkQLQgm5" />
  <component name="ProjectViewState">
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "HTTP Request.generated-requests | #3.executor": "Run",
    "JUnit.StatisticsServiceTest.testSaveStatistics.executor": "Run",
    "RequestMappingsPanelOrder0": "0",
    "RequestMappingsPanelOrder1": "1",
    "RequestMappingsPanelWidth0": "75",
    "RequestMappingsPanelWidth1": "75",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "Spring Boot.CodeReviewApplication.executor": "Debug",
    "ignore.virus.scanning.warn.message": "true",
    "kotlin-language-version-configured": "true",
    "last_opened_file_path": "D:/AICodeView/AiCodeView-V2",
    "node.js.detected.package.eslint": "true",
    "node.js.detected.package.tslint": "true",
    "node.js.selected.package.eslint": "(autodetect)",
    "node.js.selected.package.tslint": "(autodetect)",
    "nodejs_package_manager_path": "npm",
    "settings.editor.selected.configurable": "MavenSettings",
    "vue.rearranger.settings.migration": "true"
  }
}]]></component>
  <component name="ReactorSettings">
    <option name="notificationShown" value="true" />
  </component>
  <component name="RunManager" selected="Spring Boot.CodeReviewApplication">
    <configuration name="generated-requests | #3" type="HttpClient.HttpRequestRunConfigurationType" factoryName="HTTP Request" temporary="true" nameIsGenerated="true" path="$APPLICATION_CONFIG_DIR$/scratches/generated-requests.http" executionIdentifier="#3" index="3" runType="Run single request">
      <method v="2" />
    </configuration>
    <configuration name="StatisticsServiceTest.testSaveStatistics" type="JUnit" factoryName="JUnit" temporary="true" nameIsGenerated="true">
      <module name="code-review-backend" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.aicodereview.service.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <option name="PACKAGE_NAME" value="com.aicodereview.service" />
      <option name="MAIN_CLASS_NAME" value="com.aicodereview.service.StatisticsServiceTest" />
      <option name="METHOD_NAME" value="testSaveStatistics" />
      <option name="TEST_OBJECT" value="method" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration default="true" type="JetRunConfigurationType">
      <module name="AiCodeView-V2" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="CodeReviewApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <option name="FRAME_DEACTIVATION_UPDATE_POLICY" value="UpdateClassesAndResources" />
      <module name="code-review-backend" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.aicodereview.CodeReviewApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <recent_temporary>
      <list>
        <item itemvalue="HTTP Request.generated-requests | #3" />
        <item itemvalue="JUnit.StatisticsServiceTest.testSaveStatistics" />
      </list>
    </recent_temporary>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-jdk-9823dce3aa75-fdfe4dae3a2d-intellij.indexing.shared.core-IU-243.21565.193" />
        <option value="bundled-js-predefined-d6986cc7102b-e768b9ed790e-JavaScript-IU-243.21565.193" />
      </set>
    </attachedChunks>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="1d562355-c2e1-4e82-87de-4554c003342a" name="Changes" comment="" />
      <created>1754380917039</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1754380917039</updated>
      <workItem from="1754380918630" duration="1843000" />
      <workItem from="1754382796993" duration="9619000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="XDebuggerManager">
    <breakpoint-manager>
      <breakpoints>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/com/aicodereview/controller/StatisticsController.java</url>
          <line>47</line>
          <option name="timeStamp" value="5" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/com/aicodereview/controller/CodeReviewController.java</url>
          <line>102</line>
          <option name="timeStamp" value="6" />
        </line-breakpoint>
      </breakpoints>
    </breakpoint-manager>
  </component>
</project>