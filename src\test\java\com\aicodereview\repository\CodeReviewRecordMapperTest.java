package com.aicodereview.repository;

import com.aicodereview.entity.CodeReviewRecord;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.jdbc.AutoConfigureTestDatabase;
import org.springframework.test.context.ActiveProfiles;

import java.time.LocalDateTime;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * CodeReviewRecordMapper测试类
 */

@ActiveProfiles("test")
@AutoConfigureTestDatabase(replace = AutoConfigureTestDatabase.Replace.NONE)
public class CodeReviewRecordMapperTest {

    @Autowired
    private CodeReviewRecordMapper mapper;

    @Test
    public void testInsert() {
        // 创建测试记录
        CodeReviewRecord record = new CodeReviewRecord();
        record.setProgrammingLanguage("Java");
        record.setSuggestionCount(10);
        record.setAdoptedCount(8);
        record.setCreatedAt(LocalDateTime.now());

        // 插入记录
        int result = mapper.insert(record);
        assertEquals(1, result);
        assertNotNull(record.getId());
    }

    @Test
    public void testSelectById() {
        // 先插入一条记录
        CodeReviewRecord record = new CodeReviewRecord();
        record.setProgrammingLanguage("Python");
        record.setSuggestionCount(5);
        record.setAdoptedCount(3);
        record.setCreatedAt(LocalDateTime.now());
        
        mapper.insert(record);
        Long id = record.getId();

        // 查询记录
        CodeReviewRecord found = mapper.selectById(id);
        assertNotNull(found);
        assertEquals("Python", found.getProgrammingLanguage());
        assertEquals(5, found.getSuggestionCount());
        assertEquals(3, found.getAdoptedCount());
    }

    @Test
    public void testSelectList() {
        // 插入测试数据
        CodeReviewRecord record1 = new CodeReviewRecord();
        record1.setProgrammingLanguage("Java");
        record1.setSuggestionCount(10);
        record1.setAdoptedCount(8);
        record1.setCreatedAt(LocalDateTime.now());
        mapper.insert(record1);

        CodeReviewRecord record2 = new CodeReviewRecord();
        record2.setProgrammingLanguage("Python");
        record2.setSuggestionCount(5);
        record2.setAdoptedCount(3);
        record2.setCreatedAt(LocalDateTime.now());
        mapper.insert(record2);

        // 查询所有记录
        List<CodeReviewRecord> records = mapper.selectList(null);
        assertTrue(records.size() >= 2);
    }

    @Test
    public void testSelectCount() {
        // 插入测试数据
        CodeReviewRecord record = new CodeReviewRecord();
        record.setProgrammingLanguage("JavaScript");
        record.setSuggestionCount(7);
        record.setAdoptedCount(5);
        record.setCreatedAt(LocalDateTime.now());
        mapper.insert(record);

        // 统计记录数
        Long count = mapper.selectCount(null);
        assertTrue(count >= 1);
    }
}
