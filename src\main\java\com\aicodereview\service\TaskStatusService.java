package com.aicodereview.service;

import com.aicodereview.model.TaskStatus;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.locks.ReentrantReadWriteLock;

/**
 * 任务状态管理服务
 */
@Service
public class TaskStatusService {

    private static final Logger logger = LoggerFactory.getLogger(TaskStatusService.class);

    private final ConcurrentHashMap<String, TaskStatus> statusStore = new ConcurrentHashMap<>();
    private final ReentrantReadWriteLock lock = new ReentrantReadWriteLock();

    /**
     * 开始新任务
     */
    public void startTask(String resultDir, int totalFiles) {
        lock.writeLock().lock();
        try {
            TaskStatus status = new TaskStatus(resultDir, totalFiles);
            statusStore.put(resultDir, status);
            logger.info("开始新任务: {}, 总文件数: {}", resultDir, totalFiles);
        } finally {
            lock.writeLock().unlock();
        }
    }

    /**
     * 更新任务状态
     */
    public void updateStatus(String resultDir, TaskStatus.Status status, String currentFile, String error) {
        lock.writeLock().lock();
        try {
            TaskStatus taskStatus = statusStore.get(resultDir);
            if (taskStatus != null) {
                taskStatus.setStatus(status);
                if (currentFile != null) {
                    taskStatus.setCurrentFile(currentFile);
                }
                if (error != null) {
                    taskStatus.setError(error);
                }
                if (status == TaskStatus.Status.COMPLETED || status == TaskStatus.Status.FAILED) {
                    taskStatus.setEndTime(LocalDateTime.now());
                }
                logger.debug("更新任务状态: {} -> {}", resultDir, status);
            }
        } finally {
            lock.writeLock().unlock();
        }
    }

    /**
     * 增加处理进度
     */
    public void incrementProgress(String resultDir) {
        lock.writeLock().lock();
        try {
            TaskStatus taskStatus = statusStore.get(resultDir);
            if (taskStatus != null) {
                taskStatus.incrementProgress();
                logger.debug("任务进度更新: {} -> {}%", resultDir, taskStatus.getProgress());
            }
        } finally {
            lock.writeLock().unlock();
        }
    }

    /**
     * 获取任务状态
     */
    public TaskStatus getStatus(String resultDir) {
        lock.readLock().lock();
        try {
            return statusStore.get(resultDir);
        } finally {
            lock.readLock().unlock();
        }
    }

    /**
     * 标记任务完成
     */
    public void markCompleted(String resultDir) {
        lock.writeLock().lock();
        try {
            TaskStatus taskStatus = statusStore.get(resultDir);
            if (taskStatus != null) {
                taskStatus.markCompleted();
                logger.info("任务完成: {}", resultDir);
            }
        } finally {
            lock.writeLock().unlock();
        }
    }

    /**
     * 标记任务失败
     */
    public void markFailed(String resultDir, String error) {
        lock.writeLock().lock();
        try {
            TaskStatus taskStatus = statusStore.get(resultDir);
            if (taskStatus != null) {
                taskStatus.markFailed(error);
                logger.error("任务失败: {}, 错误: {}", resultDir, error);
            }
        } finally {
            lock.writeLock().unlock();
        }
    }

    /**
     * 定时清理过期任务 (每小时执行一次)
     */
    @Scheduled(fixedRate = 3600000) // 1小时
    public void cleanupOldTasks() {
        lock.writeLock().lock();
        try {
            LocalDateTime cutoffTime = LocalDateTime.now().minusHours(24);
            int removedCount = 0;

            var iterator = statusStore.entrySet().iterator();
            while (iterator.hasNext()) {
                var entry = iterator.next();
                TaskStatus status = entry.getValue();
                
                if (status.getEndTime() != null && status.getEndTime().isBefore(cutoffTime)) {
                    iterator.remove();
                    removedCount++;
                }
            }

            if (removedCount > 0) {
                logger.info("清理了 {} 个过期任务", removedCount);
            }
        } finally {
            lock.writeLock().unlock();
        }
    }

    /**
     * 获取当前任务数量
     */
    public int getActiveTaskCount() {
        lock.readLock().lock();
        try {
            return statusStore.size();
        } finally {
            lock.readLock().unlock();
        }
    }
}
