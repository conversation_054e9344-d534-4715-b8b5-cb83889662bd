package com.aicodereview.dto;

import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * 统计请求DTO
 */
public class StatisticsRequest {
    
    @JsonProperty("programmingLanguage")
    private String programmingLanguage;
    
    @JsonProperty("suggestionCount")
    private Integer suggestionCount;
    
    @JsonProperty("adoptedCount")
    private Integer adoptedCount;
    
    @JsonProperty("sessionDuration")
    private Integer sessionDuration;

    // Constructors
    public StatisticsRequest() {}

    public StatisticsRequest(String programmingLanguage, Integer suggestionCount, Integer adoptedCount) {
        this.programmingLanguage = programmingLanguage;
        this.suggestionCount = suggestionCount;
        this.adoptedCount = adoptedCount;
    }

    public StatisticsRequest(String programmingLanguage, Integer suggestionCount, Integer adoptedCount, Integer sessionDuration) {
        this.programmingLanguage = programmingLanguage;
        this.suggestionCount = suggestionCount;
        this.adoptedCount = adoptedCount;
        this.sessionDuration = sessionDuration;
    }

    // Getters and Setters
    public String getProgrammingLanguage() {
        return programmingLanguage;
    }

    public void setProgrammingLanguage(String programmingLanguage) {
        this.programmingLanguage = programmingLanguage;
    }

    public Integer getSuggestionCount() {
        return suggestionCount;
    }

    public void setSuggestionCount(Integer suggestionCount) {
        this.suggestionCount = suggestionCount;
    }

    public Integer getAdoptedCount() {
        return adoptedCount;
    }

    public void setAdoptedCount(Integer adoptedCount) {
        this.adoptedCount = adoptedCount;
    }

    public Integer getSessionDuration() {
        return sessionDuration;
    }

    public void setSessionDuration(Integer sessionDuration) {
        this.sessionDuration = sessionDuration;
    }

    @Override
    public String toString() {
        return "StatisticsRequest{" +
                "programmingLanguage='" + programmingLanguage + '\'' +
                ", suggestionCount=" + suggestionCount +
                ", adoptedCount=" + adoptedCount +
                ", sessionDuration=" + sessionDuration +
                '}';
    }
}
