package com.aicodereview.controller;

import com.aicodereview.model.ReviewProblem;
import com.aicodereview.model.StandardReviewReport;
import com.aicodereview.service.CodeReviewService;
import com.aicodereview.service.FileService;
import com.aicodereview.service.TaskStatusService;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.ResponseEntity;

import java.util.Arrays;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class CodeReviewControllerTest {

    @Mock
    private CodeReviewService codeReviewService;
    
    @Mock
    private FileService fileService;
    
    @Mock
    private TaskStatusService taskStatusService;

    private CodeReviewController controller;
    private ObjectMapper objectMapper;

    @BeforeEach
    void setUp() {
        objectMapper = new ObjectMapper();
        controller = new CodeReviewController(
            codeReviewService, 
            fileService, 
            taskStatusService, 
            objectMapper
        );
    }

    @Test
    void testGetReviewReturnsProperJsonStructure() throws Exception {
        // 准备测试数据
        String testDirPath = "test_review_dir";
        
        // 创建测试的JSON字符串（模拟文件内容）
        ReviewProblem problem = new ReviewProblem(
            1,
            "AiPlcControlServiceV2Impl.java:122",
            "在遥调失败重试时使用了ScheduledExecutorService，但未处理重试过程中的异常，可能导致线程泄漏或任务堆积",
            "高",
            "在重试任务内部添加异常捕获，确保任务异常不会导致线程终止；同时应限制重试次数，避免无限重试。"
        );
        
        StandardReviewReport originalReport = new StandardReviewReport(1, Arrays.asList(problem));
        String jsonContent = objectMapper.writeValueAsString(originalReport);
        
        // 模拟文件服务返回JSON字符串
        when(fileService.readFileContent(testDirPath, "review.json")).thenReturn(jsonContent);
        
        // 执行测试
        ResponseEntity<?> response = controller.getReview(testDirPath);
        
        // 验证响应
        assertNotNull(response);
        assertEquals(200, response.getStatusCodeValue());
        
        // 验证响应体结构
        Object responseBody = response.getBody();
        assertNotNull(responseBody);
        
        // 将响应转换为JSON字符串来检查结构
        String responseJson = objectMapper.writeValueAsString(responseBody);
        
        // 验证JSON结构 - 应该没有转义字符
        assertFalse(responseJson.contains("\\\""), "响应JSON不应包含转义字符");
        assertTrue(responseJson.contains("\"total_problems\":1"), "应包含total_problems字段");
        assertTrue(responseJson.contains("\"problem_id\":1"), "应包含problem_id字段");
        assertTrue(responseJson.contains("\"location\":\"AiPlcControlServiceV2Impl.java:122\""), "应包含location字段");
        assertTrue(responseJson.contains("\"severity\":\"高\""), "应包含severity字段");
        
        System.out.println("响应JSON结构:");
        System.out.println(responseJson);
    }

    @Test
    void testGetReviewWithMultipleProblems() throws Exception {
        // 测试多个问题的情况
        String testDirPath = "test_review_dir";
        
        ReviewProblem problem1 = new ReviewProblem(
            1,
            "AlarmEventServiceImpl.java",
            "多处未检查对象是否为null就直接调用方法或访问属性",
            "高",
            "添加null检查或使用Optional处理可能的null值"
        );
        
        ReviewProblem problem2 = new ReviewProblem(
            2,
            "AlarmEventServiceImpl.java",
            "资源泄漏风险，SimpleExcelSheetBuilder没有显式关闭",
            "高",
            "使用try-with-resources确保资源被正确关闭"
        );
        
        StandardReviewReport originalReport = new StandardReviewReport(2, Arrays.asList(problem1, problem2));
        String jsonContent = objectMapper.writeValueAsString(originalReport);
        
        when(fileService.readFileContent(testDirPath, "review.json")).thenReturn(jsonContent);
        
        ResponseEntity<?> response = controller.getReview(testDirPath);
        
        assertNotNull(response);
        assertEquals(200, response.getStatusCodeValue());
        
        String responseJson = objectMapper.writeValueAsString(response.getBody());
        
        // 验证多个问题的JSON结构
        assertTrue(responseJson.contains("\"total_problems\":2"));
        assertTrue(responseJson.contains("\"problem_id\":1"));
        assertTrue(responseJson.contains("\"problem_id\":2"));
        assertFalse(responseJson.contains("\\\""), "不应包含转义字符");
        
        System.out.println("多问题响应JSON结构:");
        System.out.println(responseJson);
    }
}
