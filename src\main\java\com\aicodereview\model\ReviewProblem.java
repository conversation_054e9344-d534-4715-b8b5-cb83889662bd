package com.aicodereview.model;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * 评审问题实体类
 * 表示代码评审中发现的单个问题
 */
public class ReviewProblem {
    
    @JsonProperty("problem_id")
    private final int problemId;
    
    @JsonProperty("location")
    private final String location;
    
    @JsonProperty("description")
    private final String description;
    
    @JsonProperty("severity")
    private final String severity;
    
    @JsonProperty("suggestion")
    private final String suggestion;

    @JsonCreator
    public ReviewProblem(
            @JsonProperty("problem_id") int problemId, 
            @JsonProperty("location") String location, 
            @JsonProperty("description") String description, 
            @JsonProperty("severity") String severity, 
            @JsonProperty("suggestion") String suggestion) {
        this.problemId = problemId;
        this.location = location;
        this.description = description;
        this.severity = severity;
        this.suggestion = suggestion;
    }

    public int getProblemId() {
        return problemId;
    }

    public String getLocation() {
        return location;
    }

    public String getDescription() {
        return description;
    }

    public String getSeverity() {
        return severity;
    }

    public String getSuggestion() {
        return suggestion;
    }

    @Override
    public String toString() {
        return "ReviewProblem{" +
                "problemId=" + problemId +
                ", location='" + location + '\'' +
                ", description='" + description + '\'' +
                ", severity='" + severity + '\'' +
                ", suggestion='" + suggestion + '\'' +
                '}';
    }
}
