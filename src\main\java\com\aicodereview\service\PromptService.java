package com.aicodereview.service;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.Resource;
import org.springframework.core.io.ResourceLoader;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;

/**
 * 提示词管理服务
 */
@Service
public class PromptService {

    private static final Logger logger = LoggerFactory.getLogger(PromptService.class);

    private final ResourceLoader resourceLoader;
    
    @Value("${app.prompt.directory:classpath:prompts/}")
    private String promptDirectory;

    public PromptService(ResourceLoader resourceLoader) {
        this.resourceLoader = resourceLoader;
    }

    /**
     * 加载提示词模板
     */
    public List<PromptTemplate> loadPromptTemplates() {
        List<PromptTemplate> templates = new ArrayList<>();
        
        try {
            // 加载默认提示词文件
            String[] promptFiles = {
                "code_structure.txt",
                "best_practices.txt", 
                "bug_detection.txt",
                "performance.txt",
                "security.txt",
                "maintainability.txt"
            };

            for (String fileName : promptFiles) {
                try {
                    Resource resource = resourceLoader.getResource(promptDirectory + fileName);
                    if (resource.exists()) {
                        String content = resource.getContentAsString(StandardCharsets.UTF_8);
                        if (StringUtils.hasText(content)) {
                            String ruleName = fileName.replace(".txt", "").replace("_", " ");
                            templates.add(new PromptTemplate(ruleName, content.trim()));
                        }
                    }
                } catch (IOException e) {
                    logger.warn("无法加载提示词文件: {}", fileName, e);
                }
            }

            // 如果没有加载到任何提示词，使用默认提示词
            if (templates.isEmpty()) {
                templates.add(getDefaultPromptTemplate());
            }

        } catch (Exception e) {
            logger.error("加载提示词模板失败，使用默认提示词", e);
            templates.add(getDefaultPromptTemplate());
        }

        logger.info("加载了 {} 个提示词模板", templates.size());
        return templates;
    }

    /**
     * 获取默认提示词模板
     */
    private PromptTemplate getDefaultPromptTemplate() {
        String defaultPrompt = """
            作为一名资深Java代码评审员，请对以下Java代码进行全面评审。
            请提供详细的反馈，包括但不限于：
            1. 代码结构和组织
            2. 遵循Java最佳实践的情况
            3. 潜在的bug或问题
            4. 性能考虑
            5. 安全问题
            6. 可读性和可维护性
            7. 建议的改进
            
            请以专业、建设性的方式提供评审意见。
            """;
        
        return new PromptTemplate("默认代码评审", defaultPrompt);
    }

    /**
     * 根据自定义问题创建提示词模板
     */
    public List<PromptTemplate> createCustomPromptTemplate(String question) {
        if (!StringUtils.hasText(question)) {
            return loadPromptTemplates();
        }
        
        return List.of(new PromptTemplate("用户自定义问题", question.trim()));
    }

    /**
     * 提示词模板类
     */
    public static class PromptTemplate {
        private final String ruleName;
        private final String promptContent;

        public PromptTemplate(String ruleName, String promptContent) {
            this.ruleName = ruleName;
            this.promptContent = promptContent;
        }

        public String getRuleName() {
            return ruleName;
        }

        public String getPromptContent() {
            return promptContent;
        }

        @Override
        public String toString() {
            return "PromptTemplate{" +
                    "ruleName='" + ruleName + '\'' +
                    ", promptContent='" + promptContent.substring(0, Math.min(50, promptContent.length())) + "...'" +
                    '}';
        }
    }
}
