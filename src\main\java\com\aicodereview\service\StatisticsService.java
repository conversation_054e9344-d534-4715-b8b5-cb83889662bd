package com.aicodereview.service;

import com.aicodereview.dto.StatisticsResponse;
import com.aicodereview.entity.CodeReviewRecord;
import com.aicodereview.repository.CodeReviewRecordMapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 统计服务
 */
@Service
@Transactional
public class StatisticsService {

    private static final Logger logger = LoggerFactory.getLogger(StatisticsService.class);

    private final CodeReviewRecordMapper mapper;

    @Autowired
    public StatisticsService(CodeReviewRecordMapper mapper) {
        this.mapper = mapper;
    }

    /**
     * 保存代码评审统计记录
     */
    public void saveStatistics(String programmingLanguage, Integer suggestionCount, Integer adoptedCount) {
        try {
            CodeReviewRecord record = new CodeReviewRecord(programmingLanguage, suggestionCount, adoptedCount);
            mapper.insert(record);

            logger.info("保存统计记录: 语言={}, 建议数={}, 采纳数={}",
                programmingLanguage, suggestionCount, adoptedCount);

        } catch (Exception e) {
            logger.error("保存统计记录失败", e);
            throw new RuntimeException("保存统计记录失败", e);
        }
    }

    /**
     * 保存代码评审统计记录（包含会话时长）
     */
    public void saveStatistics(String programmingLanguage, Integer suggestionCount, Integer adoptedCount, Integer sessionDuration) {
        try {
            CodeReviewRecord record = new CodeReviewRecord(programmingLanguage, suggestionCount, adoptedCount, sessionDuration);
            mapper.insert(record);

            logger.info("保存统计记录: 语言={}, 建议数={}, 采纳数={}, 会话时长={}秒",
                programmingLanguage, suggestionCount, adoptedCount, sessionDuration);

        } catch (Exception e) {
            logger.error("保存统计记录失败", e);
            throw new RuntimeException("保存统计记录失败", e);
        }
    }

    /**
     * 获取按编程语言分组的统计信息
     */
    @Transactional(readOnly = true)
    public List<StatisticsResponse> getStatisticsByProgrammingLanguage() {
        try {
            List<CodeReviewRecordMapper.StatisticsProjection> projections =
                mapper.getStatisticsByProgrammingLanguage();

            List<StatisticsResponse> responses = projections.stream()
                .map(p -> new StatisticsResponse(
                    p.getProgrammingLanguage(),
                    p.getTotalSuggestionCount(),
                    p.getTotalAdoptedCount()
                ))
                .collect(Collectors.toList());

            logger.debug("获取统计信息: {} 种编程语言", responses.size());
            return responses;

        } catch (Exception e) {
            logger.error("获取统计信息失败", e);
            throw new RuntimeException("获取统计信息失败", e);
        }
    }

    /**
     * 分页获取所有记录
     */
    @Transactional(readOnly = true)
    public IPage<CodeReviewRecord> getAllRecords(int pageNum, int pageSize) {
        try {
            Page<CodeReviewRecord> page = new Page<>(pageNum, pageSize);
            IPage<CodeReviewRecord> records = mapper.selectAllByOrderByLogtimeDesc(page);
            logger.debug("分页获取记录: 页码={}, 大小={}, 总数={}",
                pageNum, pageSize, records.getTotal());
            return records;

        } catch (Exception e) {
            logger.error("分页获取记录失败", e);
            throw new RuntimeException("分页获取记录失败", e);
        }
    }

    /**
     * 获取所有记录
     */
    @Transactional(readOnly = true)
    public List<CodeReviewRecord> getAllRecords() {
        try {
            List<CodeReviewRecord> records = mapper.selectList(null);
            logger.debug("获取所有记录: {} 条", records.size());
            return records;

        } catch (Exception e) {
            logger.error("获取所有记录失败", e);
            throw new RuntimeException("获取所有记录失败", e);
        }
    }

    /**
     * 获取记录总数
     */
    @Transactional(readOnly = true)
    public long getTotalRecordCount() {
        try {
            Long count = mapper.selectCount(null);
            logger.debug("记录总数: {}", count);
            return count != null ? count : 0L;

        } catch (Exception e) {
            logger.error("获取记录总数失败", e);
            throw new RuntimeException("获取记录总数失败", e);
        }
    }
}
