-- 数据库表结构迁移脚本
-- 从旧的表结构迁移到新的表结构

-- 1. 备份现有数据（如果表存在）
DO $$
BEGIN
    IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'code_review_record') THEN
        -- 创建备份表
        CREATE TABLE code_review_record_backup AS SELECT * FROM code_review_record;
        RAISE NOTICE '已创建备份表 code_review_record_backup';
    END IF;
END $$;

-- 2. 删除旧表（如果存在）
DROP TABLE IF EXISTS code_review_record CASCADE;

-- 3. 创建新表结构（按照提供的DDL）
CREATE TABLE public.code_review_record ( 
    id serial4 NOT NULL, 
    logtime timestamptz DEFAULT CURRENT_TIMESTAMP NOT NULL, 
    programming_language varchar(50) NOT NULL, 
    suggestion_count int4 NOT NULL, 
    adopted_count int4 NOT NULL, 
    session_duration int4 NULL, 
    CONSTRAINT code_review_record_adopted_count_check CHECK ((adopted_count >= 0)), 
    CONSTRAINT code_review_record_pkey PRIMARY KEY (id), 
    CONSTRAINT code_review_record_suggestion_count_check CHECK ((suggestion_count >= 0)) 
);

-- 4. 创建索引
CREATE INDEX IF NOT EXISTS idx_code_review_record_language ON code_review_record(programming_language);
CREATE INDEX IF NOT EXISTS idx_code_review_record_logtime ON code_review_record(logtime);

-- 5. 迁移数据（如果备份表存在）
DO $$
BEGIN
    IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'code_review_record_backup') THEN
        -- 从备份表迁移数据
        INSERT INTO code_review_record (programming_language, suggestion_count, adopted_count, logtime, session_duration)
        SELECT 
            programming_language,
            suggestion_count,
            adopted_count,
            CASE 
                WHEN created_at IS NOT NULL THEN created_at::timestamptz
                ELSE CURRENT_TIMESTAMP
            END as logtime,
            NULL as session_duration  -- 旧数据没有session_duration，设为NULL
        FROM code_review_record_backup;
        
        RAISE NOTICE '已从备份表迁移 % 条记录', (SELECT COUNT(*) FROM code_review_record_backup);
    END IF;
END $$;

-- 6. 插入示例数据（如果表为空）
INSERT INTO code_review_record (programming_language, suggestion_count, adopted_count, session_duration) 
SELECT 'Java', 50, 30, 300
WHERE NOT EXISTS (SELECT 1 FROM code_review_record LIMIT 1);

INSERT INTO code_review_record (programming_language, suggestion_count, adopted_count, session_duration) 
SELECT 'Python', 25, 20, 180
WHERE NOT EXISTS (SELECT 1 FROM code_review_record WHERE programming_language = 'Python');

INSERT INTO code_review_record (programming_language, suggestion_count, adopted_count, session_duration) 
SELECT 'JavaScript', 40, 25, 240
WHERE NOT EXISTS (SELECT 1 FROM code_review_record WHERE programming_language = 'JavaScript');

-- 7. 验证迁移结果
SELECT 
    COUNT(*) as total_records,
    COUNT(DISTINCT programming_language) as unique_languages,
    MIN(logtime) as earliest_record,
    MAX(logtime) as latest_record
FROM code_review_record;

-- 8. 显示表结构
\d code_review_record;

RAISE NOTICE '迁移完成！';
