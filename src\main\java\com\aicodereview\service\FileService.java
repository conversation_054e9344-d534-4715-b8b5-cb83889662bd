package com.aicodereview.service;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.nio.file.*;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

/**
 * 文件处理服务
 */
@Service
public class FileService {

    private static final Logger logger = LoggerFactory.getLogger(FileService.class);

    @Value("${app.file.upload-dir:./results}")
    private String uploadDir;

    @Value("${app.file.temp-dir:./temp}")
    private String tempDir;

    /**
     * 创建结果目录
     */
    public String createResultDirectory() {
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"));
        String uuid = UUID.randomUUID().toString().substring(0, 8);
        String resultDir = String.format("review_%s_%s", timestamp, uuid);
        
        Path resultPath = Paths.get(uploadDir, resultDir);
        try {
            Files.createDirectories(resultPath);
            logger.info("创建结果目录: {}", resultPath);
            return resultDir;
        } catch (IOException e) {
            logger.error("创建结果目录失败: {}", resultPath, e);
            throw new RuntimeException("创建结果目录失败", e);
        }
    }

    /**
     * 保存上传的文件
     */
    public List<CodeReviewService.FileInfo> saveUploadedFiles(List<MultipartFile> files, String resultDir) {
        List<CodeReviewService.FileInfo> fileInfos = new ArrayList<>();
        Path resultPath = Paths.get(uploadDir, resultDir);

        for (MultipartFile file : files) {
            if (!isJavaFile(file.getOriginalFilename())) {
                logger.warn("跳过非Java文件: {}", file.getOriginalFilename());
                continue;
            }

            try {
                Path filePath = resultPath.resolve(file.getOriginalFilename());
                Files.copy(file.getInputStream(), filePath, StandardCopyOption.REPLACE_EXISTING);
                
                fileInfos.add(new CodeReviewService.FileInfo(
                    file.getOriginalFilename(), 
                    filePath.toString()
                ));
                
                logger.debug("保存文件: {}", filePath);
                
            } catch (IOException e) {
                logger.error("保存文件失败: {}", file.getOriginalFilename(), e);
                throw new RuntimeException("保存文件失败: " + file.getOriginalFilename(), e);
            }
        }

        if (fileInfos.isEmpty()) {
            throw new IllegalArgumentException("未找到有效的Java文件");
        }

        logger.info("保存了 {} 个Java文件到目录: {}", fileInfos.size(), resultDir);
        return fileInfos;
    }

    /**
     * 保存评审结果
     */
    public void saveReviewResult(String resultDir, CodeReviewService.FileInfo fileInfo, 
                                String ruleName, String reviewResult) {
        try {
            Path resultPath = Paths.get(uploadDir, resultDir);
            String fileName = String.format("%s_%s_review.md", 
                fileInfo.getFilename().replace(".java", ""), 
                ruleName.replace(" ", "_"));
            
            Path reviewFile = resultPath.resolve(fileName);
            Files.writeString(reviewFile, reviewResult, StandardOpenOption.CREATE, StandardOpenOption.TRUNCATE_EXISTING);
            
            logger.debug("保存评审结果: {}", reviewFile);
            
        } catch (IOException e) {
            logger.error("保存评审结果失败: {} - {}", fileInfo.getFilename(), ruleName, e);
        }
    }

    /**
     * 保存汇总报告
     */
    public void saveSummaryReport(String resultDir, String summaryJson) {
        try {
            Path resultPath = Paths.get(uploadDir, resultDir);
            Path summaryFile = resultPath.resolve("review.json");
            Files.writeString(summaryFile, summaryJson, StandardOpenOption.CREATE, StandardOpenOption.TRUNCATE_EXISTING);
            
            logger.debug("保存汇总报告: {}", summaryFile);
            
        } catch (IOException e) {
            logger.error("保存汇总报告失败: {}", resultDir, e);
        }
    }

    /**
     * 创建ZIP压缩包
     */
    public Path createZipFile(String resultDir) throws IOException {
        Path resultPath = Paths.get(uploadDir, resultDir);
        if (!Files.exists(resultPath) || !Files.isDirectory(resultPath)) {
            throw new IllegalArgumentException("目录不存在: " + resultDir);
        }

        // 创建临时ZIP文件
        Path tempPath = Paths.get(tempDir);
        Files.createDirectories(tempPath);
        
        Path zipFile = tempPath.resolve(resultDir + ".zip");
        
        try (ZipOutputStream zos = new ZipOutputStream(Files.newOutputStream(zipFile))) {
            Files.walk(resultPath)
                .filter(Files::isRegularFile)
                .forEach(file -> {
                    try {
                        String entryName = resultPath.relativize(file).toString();
                        ZipEntry entry = new ZipEntry(entryName);
                        zos.putNextEntry(entry);
                        Files.copy(file, zos);
                        zos.closeEntry();
                    } catch (IOException e) {
                        logger.error("添加文件到ZIP失败: {}", file, e);
                    }
                });
        }

        logger.info("创建ZIP文件: {}", zipFile);
        return zipFile;
    }

    /**
     * 获取目录下的文件列表
     */
    public List<String> listFiles(String directory) throws IOException {
        Path dirPath = Paths.get(uploadDir, directory);
        if (!Files.exists(dirPath) || !Files.isDirectory(dirPath)) {
            throw new IllegalArgumentException("目录不存在: " + directory);
        }

        List<String> files = new ArrayList<>();
        try (DirectoryStream<Path> stream = Files.newDirectoryStream(dirPath)) {
            for (Path entry : stream) {
                if (Files.isRegularFile(entry)) {
                    files.add("📄 " + entry.getFileName().toString());
                } else if (Files.isDirectory(entry)) {
                    files.add("📁 " + entry.getFileName().toString());
                }
            }
        }

        return files;
    }

    /**
     * 读取文件内容
     */
    public String readFileContent(String directory, String fileName) throws IOException {
        Path filePath = Paths.get(uploadDir, directory, fileName);
        if (!Files.exists(filePath) || !Files.isRegularFile(filePath)) {
            throw new IllegalArgumentException("文件不存在: " + fileName);
        }

        return Files.readString(filePath);
    }

    /**
     * 获取绝对路径
     */
    public Path getAbsolutePath(String relativePath) {
        return Paths.get(uploadDir, relativePath).toAbsolutePath();
    }

    /**
     * 检查是否为Java文件
     */
    private boolean isJavaFile(String filename) {
        return filename != null && filename.toLowerCase().endsWith(".java");
    }

    /**
     * 清理临时文件
     */
    public void cleanupTempFile(Path tempFile) {
        try {
            if (Files.exists(tempFile)) {
                Files.delete(tempFile);
                logger.debug("清理临时文件: {}", tempFile);
            }
        } catch (IOException e) {
            logger.warn("清理临时文件失败: {}", tempFile, e);
        }
    }
}
