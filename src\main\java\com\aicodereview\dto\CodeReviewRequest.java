package com.aicodereview.dto;

import jakarta.validation.constraints.NotEmpty;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 代码评审请求DTO
 */
public class CodeReviewRequest {

    @NotEmpty(message = "至少需要上传一个Java文件")
    private List<MultipartFile> files;

    // Constructors
    public CodeReviewRequest() {}

    public CodeReviewRequest(List<MultipartFile> files) {
        this.files = files;
    }

    // Getters and Setters
    public List<MultipartFile> getFiles() {
        return files;
    }

    public void setFiles(List<MultipartFile> files) {
        this.files = files;
    }

    @Override
    public String toString() {
        return "CodeReviewRequest{" +
                "files=" + (files != null ? files.size() + " files" : "null") +
                '}';
    }
}
