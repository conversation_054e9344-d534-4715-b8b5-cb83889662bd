package com.aicodereview.service;

import com.aicodereview.entity.CodeReviewRecord;
import com.aicodereview.repository.CodeReviewRecordMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * StatisticsService测试类
 */
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.NONE)
@ActiveProfiles("test")
@Transactional
public class StatisticsServiceTest {

    @Autowired
    private StatisticsService statisticsService;

    @Autowired
    private CodeReviewRecordMapper mapper;

    @Test
    public void testSaveStatistics() {
        // 测试保存统计记录
        statisticsService.saveStatistics("Java", 10, 8);
        
        // 验证记录是否保存成功
        List<CodeReviewRecord> records = statisticsService.getAllRecords();
        assertFalse(records.isEmpty());
        
        CodeReviewRecord record = records.get(0);
        assertEquals("Java", record.getProgrammingLanguage());
        assertEquals(10, record.getSuggestionCount());
        assertEquals(8, record.getAdoptedCount());
        assertNotNull(record.getCreatedAt());
    }

    @Test
    public void testGetStatisticsByProgrammingLanguage() {
        // 插入测试数据
        statisticsService.saveStatistics("Java", 10, 8);
        statisticsService.saveStatistics("Python", 5, 3);
        statisticsService.saveStatistics("Java", 15, 12);
        
        // 测试按编程语言分组统计
        var statistics = statisticsService.getStatisticsByProgrammingLanguage();
        assertFalse(statistics.isEmpty());
        
        // 验证Java的统计数据
        var javaStats = statistics.stream()
            .filter(s -> "Java".equals(s.getProgrammingLanguage()))
            .findFirst()
            .orElse(null);
        
        assertNotNull(javaStats);
        assertEquals(25L, javaStats.getTotalSuggestionCount()); // 10 + 15
        assertEquals(20L, javaStats.getTotalAdoptedCount()); // 8 + 12
    }

    @Test
    public void testGetAllRecordsWithPagination() {
        // 插入测试数据
        for (int i = 0; i < 15; i++) {
            statisticsService.saveStatistics("Java", i + 1, i);
        }
        
        // 测试分页查询
        IPage<CodeReviewRecord> page1 = statisticsService.getAllRecords(1, 10);
        assertEquals(10, page1.getRecords().size());
        assertEquals(15, page1.getTotal());
        assertEquals(2, page1.getPages());
        
        IPage<CodeReviewRecord> page2 = statisticsService.getAllRecords(2, 10);
        assertEquals(5, page2.getRecords().size());
        assertEquals(15, page2.getTotal());
    }

    @Test
    public void testGetTotalRecordCount() {
        // 插入测试数据
        statisticsService.saveStatistics("Java", 10, 8);
        statisticsService.saveStatistics("Python", 5, 3);
        
        // 测试获取记录总数
        long count = statisticsService.getTotalRecordCount();
        assertEquals(2, count);
    }
}
