package com.aicodereview.service;

import com.aicodereview.service.CodeReviewService.ReviewProblem;
import com.aicodereview.service.CodeReviewService.StandardReviewReport;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Stream;

/**
 * 评审结果汇总服务
 * 负责将多个评审结果汇总成标准化的JSON格式
 */
@Service
public class ReviewSummaryService {

    private static final Logger logger = LoggerFactory.getLogger(ReviewSummaryService.class);

    private final DeepSeekService deepSeekService;
    private final ObjectMapper objectMapper;
    private final String uploadDir;

    @Autowired
    public ReviewSummaryService(DeepSeekService deepSeekService, ObjectMapper objectMapper) {
        this.deepSeekService = deepSeekService;
        this.objectMapper = objectMapper;
        this.uploadDir = System.getProperty("user.dir") + "/results";
    }

    /**
     * 生成标准化的评审汇总报告
     */
    public StandardReviewReport generateStandardReport(String resultDir, List<CodeReviewService.FileInfo> files) {
        try {
            logger.info("开始生成标准化评审报告: {}", resultDir);

            // 收集所有评审结果
            List<String> allReviewContents = collectAllReviewResults(resultDir);
            
            if (allReviewContents.isEmpty()) {
                logger.warn("未找到有效的评审结果: {}", resultDir);
                return new StandardReviewReport(0, new ArrayList<>());
            }

            // 使用AI汇总评审结果
            List<ReviewProblem> problems = summarizeWithAI(allReviewContents, files);
            
            StandardReviewReport report = new StandardReviewReport(problems.size(), problems);
            
            logger.info("标准化评审报告生成完成: {} 个问题", problems.size());
            return report;

        } catch (Exception e) {
            logger.error("生成标准化评审报告失败: {}", resultDir, e);
            return new StandardReviewReport(0, new ArrayList<>());
        }
    }

    /**
     * 收集所有评审结果文件的内容
     */
    private List<String> collectAllReviewResults(String resultDir) {
        List<String> reviewContents = new ArrayList<>();
        Path resultPath = Paths.get(uploadDir, resultDir);

        try (Stream<Path> files = Files.list(resultPath)) {
            files.filter(path -> path.toString().endsWith("_review.md"))
                 .forEach(path -> {
                     try {
                         String content = Files.readString(path);
                         // 过滤掉错误信息
                         if (!content.contains("评审过程发生错误") && !content.trim().isEmpty()) {
                             reviewContents.add(content);
                         }
                     } catch (IOException e) {
                         logger.warn("读取评审文件失败: {}", path, e);
                     }
                 });
        } catch (IOException e) {
            logger.error("读取评审结果目录失败: {}", resultPath, e);
        }

        return reviewContents;
    }

    /**
     * 使用AI汇总评审结果
     */
    private List<ReviewProblem> summarizeWithAI(List<String> reviewContents, List<CodeReviewService.FileInfo> files) {
        try {
            // 构建汇总提示词
            String summaryPrompt = buildSummaryPrompt(reviewContents, files);
            
            // 调用AI进行汇总
            String aiSummary = deepSeekService.reviewCode("", summaryPrompt);
            
            // 解析AI返回的结果
            return parseAISummary(aiSummary);
            
        } catch (Exception e) {
            logger.error("AI汇总评审结果失败", e);
            return new ArrayList<>();
        }
    }

    /**
     * 构建汇总提示词
     */
    private String buildSummaryPrompt(List<String> reviewContents, List<CodeReviewService.FileInfo> files) {
        StringBuilder prompt = new StringBuilder();
        
        prompt.append("请分析以下代码评审结果，并将其汇总成标准化的问题列表。\n\n");
        prompt.append("文件信息：\n");
        for (CodeReviewService.FileInfo file : files) {
            prompt.append("- ").append(file.getFilename()).append("\n");
        }
        
        prompt.append("\n评审结果：\n");
        for (int i = 0; i < reviewContents.size(); i++) {
            prompt.append("=== 评审结果 ").append(i + 1).append(" ===\n");
            prompt.append(reviewContents.get(i)).append("\n\n");
        }
        
        prompt.append("请按照以下JSON格式输出汇总结果，只输出JSON，不要其他内容：\n");
        prompt.append("{\n");
        prompt.append("  \"total_problems\": 问题总数,\n");
        prompt.append("  \"problems\": [\n");
        prompt.append("    {\n");
        prompt.append("      \"problem_id\": 问题编号(从1开始),\n");
        prompt.append("      \"location\": \"文件名:行号\",\n");
        prompt.append("      \"description\": \"问题描述\",\n");
        prompt.append("      \"severity\": \"严重程度(高/中/低)\",\n");
        prompt.append("      \"suggestion\": \"改进建议\"\n");
        prompt.append("    }\n");
        prompt.append("  ]\n");
        prompt.append("}\n\n");
        prompt.append("注意：\n");
        prompt.append("1. 只提取真正的代码问题，忽略一般性建议\n");
        prompt.append("2. 合并相似的问题\n");
        prompt.append("3. 如果无法确定具体行号，使用文件名\n");
        prompt.append("4. 严重程度分为：高(安全问题、严重bug)、中(性能问题、设计缺陷)、低(代码风格、轻微改进)\n");
        
        return prompt.toString();
    }

    /**
     * 解析AI返回的汇总结果
     */
    private List<ReviewProblem> parseAISummary(String aiSummary) {
        List<ReviewProblem> problems = new ArrayList<>();
        
        try {
            // 提取JSON部分
            String jsonContent = extractJsonFromResponse(aiSummary);
            if (jsonContent == null) {
                logger.warn("无法从AI响应中提取JSON内容");
                return problems;
            }
            
            // 解析JSON
            StandardReviewReport report = objectMapper.readValue(jsonContent, StandardReviewReport.class);
            return report.getProblems();
            
        } catch (Exception e) {
            logger.error("解析AI汇总结果失败: {}", aiSummary, e);
            
            // 如果JSON解析失败，尝试简单的文本解析
            return parseAISummaryAsText(aiSummary);
        }
    }

    /**
     * 从AI响应中提取JSON内容
     */
    private String extractJsonFromResponse(String response) {
        // 查找JSON开始和结束标记
        int startIndex = response.indexOf("{");
        int endIndex = response.lastIndexOf("}");
        
        if (startIndex != -1 && endIndex != -1 && endIndex > startIndex) {
            return response.substring(startIndex, endIndex + 1);
        }
        
        return null;
    }

    /**
     * 当JSON解析失败时，尝试文本解析
     */
    private List<ReviewProblem> parseAISummaryAsText(String aiSummary) {
        List<ReviewProblem> problems = new ArrayList<>();
        
        // 简单的文本解析逻辑
        String[] lines = aiSummary.split("\n");
        int problemId = 1;
        
        for (String line : lines) {
            line = line.trim();
            if (line.contains("问题") || line.contains("建议") || line.contains("改进")) {
                problems.add(new ReviewProblem(
                    problemId++,
                    "未知位置",
                    line,
                    "中",
                    "请参考评审建议进行改进"
                ));
            }
        }
        
        return problems;
    }

    /**
     * 保存标准化评审报告
     */
    public void saveStandardReport(String resultDir, StandardReviewReport report) {
        try {
            String reportJson = objectMapper.writeValueAsString(report);
            Path resultPath = Paths.get(uploadDir, resultDir);
            Path reportFile = resultPath.resolve("review.json");
            
            Files.writeString(reportFile, reportJson);
            logger.info("标准化评审报告已保存: {}", reportFile);
            
        } catch (Exception e) {
            logger.error("保存标准化评审报告失败: {}", resultDir, e);
        }
    }
}
