# Python到Java后端迁移指南

本文档详细说明如何从Python FastAPI后端迁移到Java Spring Boot后端。

## 迁移概览

### 架构对比

| 组件 | Python版本 | Java版本 |
|------|------------|----------|
| Web框架 | FastAPI | Spring Boot 3.x |
| 数据库访问 | psycopg2 | Spring Data JPA |
| 异步处理 | asyncio | Spring Async + CompletableFuture |
| HTTP客户端 | requests | Spring WebClient |
| 配置管理 | 环境变量 | Spring Boot配置文件 |
| 依赖注入 | 手动管理 | Spring IoC容器 |

### API兼容性

Java后端完全兼容现有的前端API调用，无需修改前端代码。

## 详细迁移映射

### 1. 主要服务类迁移

#### Python `web_server.py` → Java `CodeReviewController.java`

**Python代码:**
```python
@app.post("/api/code_review")
async def code_review(background_tasks: BackgroundTasks, files: List[UploadFile] = File(...)):
    # 处理逻辑
```

**Java代码:**
```java
@PostMapping(consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
public ResponseEntity<ApiResponse<CodeReviewResponse>> codeReview(
    @RequestParam("files") List<MultipartFile> files) {
    // 处理逻辑
}
```

#### Python `code_review.py` → Java `CodeReviewService.java`

**Python代码:**
```python
def generate_code_review(code: str, question: str):
    llm = create_llm_qwen3()
    # 评审逻辑
```

**Java代码:**
```java
@Async("codeReviewTaskExecutor")
public CompletableFuture<Void> reviewCodeAsync(List<FileInfo> files, String resultDir) {
    // 评审逻辑
}
```

### 2. 数据库访问迁移

#### Python `db_util.py` → Java `StatisticsService.java` + JPA

**Python代码:**
```python
def insert_user_coding_session(programming_language: str, suggestion_count: int, adopted_count: int):
    sql = '''INSERT INTO code_review_record ...'''
    with get_conn() as conn:
        with conn.cursor() as cur:
            cur.execute(sql, (programming_language, suggestion_count, adopted_count))
```

**Java代码:**
```java
@Transactional
public void saveStatistics(String programmingLanguage, Integer suggestionCount, Integer adoptedCount) {
    CodeReviewRecord record = new CodeReviewRecord(programmingLanguage, suggestionCount, adoptedCount);
    repository.save(record);
}
```

### 3. 文件处理迁移

#### Python `file_util.py` → Java `FileService.java`

**Python代码:**
```python
def save_file(result_dir: str, file, res_list):
    result_file = os.path.join(result_dir, f"{file['filename']}_review.md")
    with open(result_file, "w", encoding="utf-8") as f:
        f.write(content)
```

**Java代码:**
```java
public void saveReviewResult(String resultDir, FileInfo fileInfo, String ruleName, String reviewResult) {
    Path reviewFile = resultPath.resolve(fileName);
    Files.writeString(reviewFile, reviewResult);
}
```

### 4. 状态管理迁移

#### Python `CodeReviewStatusManager.py` → Java `TaskStatusService.java`

**Python代码:**
```python
async def update_status(self, result_dir: str, status: str, current_file: Optional[str] = None):
    async with self._lock:
        # 更新状态
```

**Java代码:**
```java
public void updateStatus(String resultDir, TaskStatus.Status status, String currentFile, String error) {
    lock.writeLock().lock();
    try {
        // 更新状态
    } finally {
        lock.writeLock().unlock();
    }
}
```

### 5. AI模型集成迁移

#### Python `model_operation.py` → Java `DeepSeekService.java`

**Python代码:**
```python
def create_llm_qwen3():
    return OpenAI(model=model_name, base_url=model_url, api_key=model_api_key)
```

**Java代码:**
```java
public String reviewCode(String code, String systemPrompt) {
    DeepSeekRequest request = createRequest(systemPrompt, code);
    return webClient.post().uri("/chat/completions").bodyValue(request)
        .retrieve().bodyToMono(DeepSeekResponse.class).block();
}
```

## 配置迁移

### 环境变量映射

| Python环境变量 | Java配置属性 | 描述 |
|----------------|--------------|------|
| `GJ_KEY` | `deepseek.api.api-key` | DeepSeek API密钥 |
| `DB_HOST` | `spring.datasource.url` | 数据库连接 |
| `UPLOAD_DIR` | `app.file.upload-dir` | 文件上传目录 |

### 配置文件迁移

**Python配置 (环境变量):**
```bash
export DEEPSEEK_API_KEY=your-api-key
export DB_HOST=************
export DB_PORT=9845
```

**Java配置 (application.yml):**
```yaml
deepseek:
  api:
    api-key: ${DEEPSEEK_API_KEY}
    base-url: https://api.deepseek.com/v1

spring:
  datasource:
    url: ********************************************
```

## 部署迁移

### 1. 停止Python服务

```bash
# 停止Python服务
pkill -f "python.*main.py"
# 或者如果使用systemd
sudo systemctl stop code-review-python
```

### 2. 部署Java服务

```bash
# 克隆Java后端代码
git clone <java-backend-repo>
cd java-backend

# 设置环境变量
export DEEPSEEK_API_KEY=your-deepseek-api-key
export SPRING_PROFILES_ACTIVE=prod

# 构建和启动
chmod +x start.sh
./start.sh
```

### 3. 使用Docker部署

```bash
# 使用Docker Compose
export DEEPSEEK_API_KEY=your-deepseek-api-key
docker-compose up -d
```

## 验证迁移

### 1. 健康检查

```bash
# 检查服务状态
curl http://localhost:8080/api/actuator/health

# 预期响应
{"status":"UP"}
```

### 2. API功能测试

```bash
# 测试文件上传
curl -X POST http://localhost:8080/api/code_review \
  -F "files=@test.java"

# 测试状态查询
curl http://localhost:8080/api/code_review/status/review_20240101_120000_abcd1234
```

### 3. 数据库连接测试

```bash
# 测试统计API
curl -X POST http://localhost:8080/api/save/statistics \
  -H "Content-Type: application/json" \
  -d '{"programming_language":"Java","suggestion_count":10,"adopted_count":5}'

curl http://localhost:8080/api/query/statistics
```

## 性能对比

### 启动时间
- **Python**: ~2-3秒
- **Java**: ~10-15秒 (首次启动，包含JVM预热)

### 内存使用
- **Python**: ~100-200MB
- **Java**: ~500MB-1GB (可通过JVM参数调优)

### 并发处理
- **Python**: 受GIL限制，适合I/O密集型
- **Java**: 真正的多线程，更好的CPU利用率

### 响应时间
- **Python**: 50-100ms (简单请求)
- **Java**: 30-80ms (预热后性能更好)

## 故障排除

### 常见问题

1. **端口冲突**
   - 确保Python服务已完全停止
   - 检查端口8080是否被占用

2. **数据库连接问题**
   - 验证数据库配置是否正确
   - 检查网络连接和防火墙设置

3. **API兼容性问题**
   - 检查请求路径是否正确 (`/api/` 前缀)
   - 验证请求参数格式

4. **文件权限问题**
   - 确保Java进程有读写权限
   - 检查上传目录是否存在

### 回滚方案

如果迁移出现问题，可以快速回滚到Python版本：

```bash
# 停止Java服务
docker-compose down
# 或者
pkill -f java

# 启动Python服务
cd /path/to/python-backend
python src/main.py
```

## 后续优化建议

1. **性能调优**
   - 调整JVM参数
   - 配置连接池大小
   - 启用缓存机制

2. **监控和日志**
   - 集成APM工具 (如Micrometer + Prometheus)
   - 配置结构化日志
   - 设置告警机制

3. **安全加固**
   - 启用HTTPS
   - 配置API限流
   - 添加认证授权

4. **高可用部署**
   - 多实例部署
   - 负载均衡配置
   - 数据库主从复制

## 总结

Java后端提供了更好的性能、类型安全和企业级特性，同时保持了与现有前端的完全兼容性。迁移过程相对平滑，主要工作集中在配置调整和服务部署上。
