package com.aicodereview.service;

import com.aicodereview.service.CodeReviewService.FileInfo;
import com.aicodereview.service.CodeReviewService.StandardReviewReport;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class CodeReviewServiceTest {

    @Mock
    private DeepSeekService deepSeekService;
    
    @Mock
    private FileService fileService;
    
    @Mock
    private TaskStatusService taskStatusService;
    
    @Mock
    private PromptService promptService;

    private CodeReviewService codeReviewService;
    private ObjectMapper objectMapper;

    @BeforeEach
    void setUp() {
        objectMapper = new ObjectMapper();
        codeReviewService = new CodeReviewService(
            deepSeekService, 
            promptService, 
            taskStatusService, 
            fileService, 
            objectMapper
        );
    }

    @Test
    void testStandardReportJsonSerialization() throws Exception {
        // 测试标准化报告的JSON序列化
        CodeReviewService.ReviewProblem problem = new CodeReviewService.ReviewProblem(
            1,
            "AiPlcControlServiceV2Impl.java:122",
            "在遥调失败重试时使用了ScheduledExecutorService，但未处理重试过程中的异常，可能导致线程泄漏或任务堆积",
            "高",
            "在重试任务内部添加异常捕获，确保任务异常不会导致线程终止；同时应限制重试次数，避免无限重试。"
        );
        
        StandardReviewReport report = new StandardReviewReport(1, Arrays.asList(problem));

        // 序列化为JSON
        String json = objectMapper.writeValueAsString(report);
        
        // 验证JSON格式
        assertTrue(json.contains("\"total_problems\":1"));
        assertTrue(json.contains("\"problem_id\":1"));
        assertTrue(json.contains("\"location\":\"AiPlcControlServiceV2Impl.java:122\""));
        assertTrue(json.contains("\"severity\":\"高\""));
        assertTrue(json.contains("ScheduledExecutorService"));

        // 反序列化
        StandardReviewReport deserializedReport = objectMapper.readValue(json, StandardReviewReport.class);
        
        // 验证反序列化结果
        assertEquals(1, deserializedReport.getTotalProblems());
        assertEquals(1, deserializedReport.getProblems().size());
        
        CodeReviewService.ReviewProblem deserializedProblem = deserializedReport.getProblems().get(0);
        assertEquals(1, deserializedProblem.getProblemId());
        assertEquals("AiPlcControlServiceV2Impl.java:122", deserializedProblem.getLocation());
        assertEquals("高", deserializedProblem.getSeverity());
    }

    @Test
    void testJsonFormatMatchesRequiredStructure() throws Exception {
        // 测试生成的JSON格式是否符合要求的结构
        CodeReviewService.ReviewProblem problem1 = new CodeReviewService.ReviewProblem(
            1,
            "AiPlcControlServiceV2Impl.java:122",
            "在遥调失败重试时使用了ScheduledExecutorService，但未处理重试过程中的异常，可能导致线程泄漏或任务堆积",
            "高",
            "在重试任务内部添加异常捕获，确保任务异常不会导致线程终止；同时应限制重试次数，避免无限重试。"
        );
        
        CodeReviewService.ReviewProblem problem2 = new CodeReviewService.ReviewProblem(
            2,
            "TestService.java:45",
            "数据库查询未使用索引，可能导致性能问题",
            "中",
            "优化查询语句，添加适当的数据库索引。"
        );
        
        StandardReviewReport report = new StandardReviewReport(2, Arrays.asList(problem1, problem2));
        String json = objectMapper.writeValueAsString(report);
        
        // 验证JSON结构符合要求的格式
        assertTrue(json.contains("\"total_problems\":2"));
        assertTrue(json.contains("\"problems\":["));
        assertTrue(json.contains("\"problem_id\":1"));
        assertTrue(json.contains("\"problem_id\":2"));
        assertTrue(json.contains("\"location\":"));
        assertTrue(json.contains("\"description\":"));
        assertTrue(json.contains("\"severity\":"));
        assertTrue(json.contains("\"suggestion\":"));
        
        System.out.println("Generated JSON:");
        System.out.println(json);
    }

    @Test
    void testEmptyReportGeneration() throws Exception {
        // 测试空报告的生成
        StandardReviewReport emptyReport = new StandardReviewReport(0, Arrays.asList());
        String json = objectMapper.writeValueAsString(emptyReport);
        
        assertTrue(json.contains("\"total_problems\":0"));
        assertTrue(json.contains("\"problems\":[]"));
        
        // 验证可以正确反序列化
        StandardReviewReport deserializedReport = objectMapper.readValue(json, StandardReviewReport.class);
        assertEquals(0, deserializedReport.getTotalProblems());
        assertTrue(deserializedReport.getProblems().isEmpty());
    }
}
