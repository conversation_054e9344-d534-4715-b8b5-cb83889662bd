import com.aicodereview.model.ReviewProblem;
import com.aicodereview.model.StandardReviewReport;
import com.aicodereview.response.ApiResponse;
import com.fasterxml.jackson.databind.ObjectMapper;

import java.util.Arrays;

/**
 * 演示修改前后JSON响应的差异
 */
public class JsonResponseExample {
    
    public static void main(String[] args) throws Exception {
        ObjectMapper objectMapper = new ObjectMapper();
        
        // 创建测试数据
        ReviewProblem problem = new ReviewProblem(
            1,
            "AiPlcControlServiceV2Impl.java:122",
            "在遥调失败重试时使用了ScheduledExecutorService，但未处理重试过程中的异常，可能导致线程泄漏或任务堆积",
            "高",
            "在重试任务内部添加异常捕获，确保任务异常不会导致线程终止；同时应限制重试次数，避免无限重试。"
        );
        
        StandardReviewReport report = new StandardReviewReport(1, Arrays.asList(problem));
        
        System.out.println("=== 修改前的方式（会产生转义字符）===");
        // 模拟修改前的方式：先序列化为字符串，再包装在ApiResponse中
        String jsonString = objectMapper.writeValueAsString(report);
        ApiResponse<String> oldWayResponse = ApiResponse.success(jsonString);
        String oldWayJson = objectMapper.writeValueAsString(oldWayResponse);
        System.out.println(oldWayJson);
        
        System.out.println("\n=== 修改后的方式（直接返回对象）===");
        // 修改后的方式：直接包装对象
        ApiResponse<StandardReviewReport> newWayResponse = ApiResponse.success(report);
        String newWayJson = objectMapper.writeValueAsString(newWayResponse);
        System.out.println(newWayJson);
        
        System.out.println("\n=== 差异分析 ===");
        System.out.println("修改前包含转义字符: " + oldWayJson.contains("\\\""));
        System.out.println("修改后包含转义字符: " + newWayJson.contains("\\\""));
    }
}
