package com.aicodereview.controller;

import com.aicodereview.dto.StatisticsRequest;
import com.aicodereview.service.StatisticsService;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.verify;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * StatisticsController测试类
 */
@WebMvcTest(StatisticsController.class)
class StatisticsControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private StatisticsService statisticsService;

    @Autowired
    private ObjectMapper objectMapper;

    @Test
    void testSaveStatisticsWithValidJson() throws Exception {
        // 准备测试数据
        StatisticsRequest request = new StatisticsRequest("java", 20, 10);
        String jsonContent = objectMapper.writeValueAsString(request);

        System.out.println("发送的JSON: " + jsonContent);

        // 执行请求
        mockMvc.perform(post("/save/statistics")
                .contentType(MediaType.APPLICATION_JSON)
                .content(jsonContent))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(0))
                .andExpect(jsonPath("$.message").value("成功"))
                .andExpect(jsonPath("$.data").value("保存成功"));

        // 验证服务方法被调用
        verify(statisticsService).saveStatistics("java", 20, 10);
    }

    @Test
    void testSaveStatisticsWithSessionDuration() throws Exception {
        // 准备测试数据（包含sessionDuration）
        StatisticsRequest request = new StatisticsRequest("python", 15, 12, 300);
        String jsonContent = objectMapper.writeValueAsString(request);

        System.out.println("发送的JSON（包含sessionDuration）: " + jsonContent);

        // 执行请求
        mockMvc.perform(post("/save/statistics")
                .contentType(MediaType.APPLICATION_JSON)
                .content(jsonContent))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(0))
                .andExpect(jsonPath("$.message").value("成功"))
                .andExpect(jsonPath("$.data").value("保存成功"));

        // 验证服务方法被调用（带sessionDuration的版本）
        verify(statisticsService).saveStatistics("python", 15, 12, 300);
    }

    @Test
    void testJsonDeserialization() throws Exception {
        // 测试JSON反序列化
        String json = """
            {
                "programmingLanguage": "java",
                "suggestionCount": 20,
                "adoptedCount": 10
            }
            """;

        StatisticsRequest request = objectMapper.readValue(json, StatisticsRequest.class);
        
        System.out.println("反序列化结果: " + request);
        
        // 验证反序列化结果
        assert request != null;
        assert "java".equals(request.getProgrammingLanguage());
        assert request.getSuggestionCount() == 20;
        assert request.getAdoptedCount() == 10;
        assert request.getSessionDuration() == null;
    }

    @Test
    void testJsonDeserializationWithSessionDuration() throws Exception {
        // 测试包含sessionDuration的JSON反序列化
        String json = """
            {
                "programmingLanguage": "python",
                "suggestionCount": 15,
                "adoptedCount": 12,
                "sessionDuration": 300
            }
            """;

        StatisticsRequest request = objectMapper.readValue(json, StatisticsRequest.class);
        
        System.out.println("反序列化结果（包含sessionDuration）: " + request);
        
        // 验证反序列化结果
        assert request != null;
        assert "python".equals(request.getProgrammingLanguage());
        assert request.getSuggestionCount() == 15;
        assert request.getAdoptedCount() == 12;
        assert request.getSessionDuration() == 300;
    }

    @Test
    void testRawJsonRequest() throws Exception {
        // 直接使用您提供的JSON格式
        String json = """
            {
                "programmingLanguage": "java",
                "suggestionCount": 20,
                "adoptedCount": 10
            }
            """;

        // 执行请求
        mockMvc.perform(post("/save/statistics")
                .contentType(MediaType.APPLICATION_JSON)
                .content(json))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(0))
                .andExpect(jsonPath("$.data").value("保存成功"));

        // 验证服务方法被调用
        verify(statisticsService).saveStatistics("java", 20, 10);
    }
}
