package com.aicodereview.entity;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.Test;

import java.time.OffsetDateTime;

import static org.junit.jupiter.api.Assertions.*;

/**
 * CodeReviewRecord实体类测试
 */
class CodeReviewRecordTest {

    @Test
    void testEntityCreation() {
        // 测试基本构造函数
        CodeReviewRecord record1 = new CodeReviewRecord("Java", 10, 8);
        assertEquals("Java", record1.getProgrammingLanguage());
        assertEquals(10, record1.getSuggestionCount());
        assertEquals(8, record1.getAdoptedCount());
        assertNull(record1.getSessionDuration());

        // 测试包含session_duration的构造函数
        CodeReviewRecord record2 = new CodeReviewRecord("Python", 5, 3, 300);
        assertEquals("Python", record2.getProgrammingLanguage());
        assertEquals(5, record2.getSuggestionCount());
        assertEquals(3, record2.getAdoptedCount());
        assertEquals(300, record2.getSessionDuration());
    }

    @Test
    void testGettersAndSetters() {
        CodeReviewRecord record = new CodeReviewRecord();
        
        // 测试ID
        record.setId(1);
        assertEquals(1, record.getId());

        // 测试编程语言
        record.setProgrammingLanguage("JavaScript");
        assertEquals("JavaScript", record.getProgrammingLanguage());

        // 测试建议数量
        record.setSuggestionCount(15);
        assertEquals(15, record.getSuggestionCount());

        // 测试采纳数量
        record.setAdoptedCount(12);
        assertEquals(12, record.getAdoptedCount());

        // 测试会话时长
        record.setSessionDuration(450);
        assertEquals(450, record.getSessionDuration());

        // 测试日志时间
        OffsetDateTime now = OffsetDateTime.now();
        record.setLogtime(now);
        assertEquals(now, record.getLogtime());
    }

    @Test
    void testToString() {
        CodeReviewRecord record = new CodeReviewRecord("Java", 10, 8, 300);
        record.setId(1);
        record.setLogtime(OffsetDateTime.now());

        String toString = record.toString();
        
        assertTrue(toString.contains("id=1"));
        assertTrue(toString.contains("programmingLanguage='Java'"));
        assertTrue(toString.contains("suggestionCount=10"));
        assertTrue(toString.contains("adoptedCount=8"));
        assertTrue(toString.contains("sessionDuration=300"));
        assertTrue(toString.contains("logtime="));
    }

    @Test
    void testJsonSerialization() throws Exception {
        ObjectMapper objectMapper = new ObjectMapper();
        
        CodeReviewRecord record = new CodeReviewRecord("Java", 10, 8, 300);
        record.setId(1);
        record.setLogtime(OffsetDateTime.now());

        // 序列化
        String json = objectMapper.writeValueAsString(record);
        assertNotNull(json);
        assertTrue(json.contains("\"programmingLanguage\":\"Java\""));
        assertTrue(json.contains("\"suggestionCount\":10"));
        assertTrue(json.contains("\"adoptedCount\":8"));
        assertTrue(json.contains("\"sessionDuration\":300"));

        // 反序列化
        CodeReviewRecord deserializedRecord = objectMapper.readValue(json, CodeReviewRecord.class);
        assertEquals(record.getId(), deserializedRecord.getId());
        assertEquals(record.getProgrammingLanguage(), deserializedRecord.getProgrammingLanguage());
        assertEquals(record.getSuggestionCount(), deserializedRecord.getSuggestionCount());
        assertEquals(record.getAdoptedCount(), deserializedRecord.getAdoptedCount());
        assertEquals(record.getSessionDuration(), deserializedRecord.getSessionDuration());
    }

    @Test
    void testFieldMapping() {
        // 验证字段映射是否正确
        CodeReviewRecord record = new CodeReviewRecord();
        
        // 这些字段应该对应数据库中的正确列名
        // id -> id (serial4)
        // programmingLanguage -> programming_language (varchar(50))
        // suggestionCount -> suggestion_count (int4)
        // adoptedCount -> adopted_count (int4)
        // sessionDuration -> session_duration (int4)
        // logtime -> logtime (timestamptz)
        
        assertNotNull(record);
        
        // 测试默认值
        assertNull(record.getId());
        assertNull(record.getProgrammingLanguage());
        assertNull(record.getSuggestionCount());
        assertNull(record.getAdoptedCount());
        assertNull(record.getSessionDuration());
        assertNull(record.getLogtime());
    }
}
