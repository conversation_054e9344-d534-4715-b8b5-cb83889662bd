version: '3.8'

services:
  # AI Code Review Backend
  code-review-backend:
    build: .
    ports:
      - "8080:8080"
    environment:
      - SPRING_PROFILES_ACTIVE=prod
      - DEEPSEEK_API_KEY=${DEEPSEEK_API_KEY}
      - JAVA_OPTS=-Xmx2g -Xms1g -XX:+UseG1GC
    volumes:
      - ./results:/app/results
      - ./temp:/app/temp
      - ./logs:/app/logs
    depends_on:
      - postgres
    restart: unless-stopped
    networks:
      - code-review-network

  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    environment:
      - POSTGRES_DB=CodeView
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=Ceiec4567%%
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql
    ports:
      - "5432:5432"
    restart: unless-stopped
    networks:
      - code-review-network

  # Nginx (可选，用于反向代理)
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
    depends_on:
      - code-review-backend
    restart: unless-stopped
    networks:
      - code-review-network

volumes:
  postgres_data:

networks:
  code-review-network:
    driver: bridge
