import com.aicodereview.dto.StatisticsRequest;
import com.fasterxml.jackson.databind.ObjectMapper;

/**
 * 测试JSON解析的简单类
 */
public class TestJsonParsing {
    
    public static void main(String[] args) {
        try {
            ObjectMapper objectMapper = new ObjectMapper();
            
            // 测试您提供的JSON格式
            String json = """
                {
                    "programmingLanguage": "java",
                    "suggestionCount": 20,
                    "adoptedCount": 10
                }
                """;
            
            System.out.println("原始JSON:");
            System.out.println(json);
            
            // 反序列化
            StatisticsRequest request = objectMapper.readValue(json, StatisticsRequest.class);
            
            System.out.println("\n反序列化结果:");
            System.out.println("request对象: " + request);
            System.out.println("programmingLanguage: " + request.getProgrammingLanguage());
            System.out.println("suggestionCount: " + request.getSuggestionCount());
            System.out.println("adoptedCount: " + request.getAdoptedCount());
            System.out.println("sessionDuration: " + request.getSessionDuration());
            
            // 测试序列化
            String serializedJson = objectMapper.writeValueAsString(request);
            System.out.println("\n重新序列化的JSON:");
            System.out.println(serializedJson);
            
            // 验证字段是否为null
            System.out.println("\n字段null检查:");
            System.out.println("programmingLanguage是否为null: " + (request.getProgrammingLanguage() == null));
            System.out.println("suggestionCount是否为null: " + (request.getSuggestionCount() == null));
            System.out.println("adoptedCount是否为null: " + (request.getAdoptedCount() == null));
            
        } catch (Exception e) {
            System.err.println("JSON解析失败:");
            e.printStackTrace();
        }
    }
}
